# AI Prompt 管理工具

一个功能强大的AI提示词管理工具，帮助您高效地组织、编辑和管理AI提示词库。

## 📝 功能特性

### 🎯 核心功能
- **提示词管理**：创建、编辑、删除和查看提示词
- **分类系统**：通过分类标签组织提示词，支持自定义图标和颜色
- **智能搜索**：快速检索提示词内容和标签
- **标签系统**：为提示词添加多个标签便于分类
- **Markdown支持**：完整的Markdown编辑和预览功能

### ✨ 编辑器功能
- **工具栏**：粗体、斜体、代码、标题、列表等常用格式
- **图片支持**：拖拽上传、工具栏插入、剪贴板粘贴图片
- **实时预览**：编辑和预览模式切换
- **Base64编码**：图片自动转换为Base64格式嵌入

### 🎨 界面设计
- **主题切换**：6种主题背景（默认、暖色、冷色、绿色、紫色、深色）
- **响应式设计**：适配不同屏幕尺寸
- **现代UI**：简洁美观的用户界面
- **平滑动效**：流畅的交互动画

### 📤 导出功能
- **Markdown导出**：直接导出为.md文件
- **文件下载**：一键下载保存到本地

### 📂 分类管理
- **预设分类**：编程、论文提示词、cursor mcp等常用分类
- **自定义分类**：创建个性化分类，选择图标和颜色
- **分类统计**：实时显示每个分类的提示词数量
- **滚动支持**：分类列表支持滚动，避免页面过长

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-prompt-manager
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动后端服务**
```bash
cd backend
python main.py
```

4. **访问应用**
打开浏览器访问：`http://localhost:5022`

## 📁 项目结构

```
ai-prompt-manager/
├── backend/                 # 后端代码
│   ├── main.py             # FastAPI主应用
│   ├── models.py           # 数据库模型
│   ├── schemas.py          # API数据模型
│   ├── crud.py             # 数据库操作
│   └── database.py         # 数据库配置
├── frontend/               # 前端代码
│   ├── index.html          # 主页面
│   ├── style.css           # 样式文件
│   └── script.js           # JavaScript逻辑
├── requirements.txt        # Python依赖
└── README.md              # 项目说明
```

## 🔧 技术栈

### 后端
- **FastAPI**：现代、快速的Web框架
- **SQLAlchemy**：Python SQL工具包和ORM
- **SQLite**：轻量级数据库
- **Uvicorn**：ASGI服务器

### 前端
- **原生HTML/CSS/JavaScript**：无框架依赖
- **Marked.js**：Markdown解析器
- **Lucide Icons**：现代图标库

## 📖 使用指南

### 创建提示词
1. 点击左侧边栏的"新建提示词"按钮
2. 填写标题、选择分类、添加标签
3. 在编辑器中输入提示词内容（支持Markdown）
4. 点击"保存"按钮

### 管理分类
1. 点击分类区域的齿轮图标打开"分类管理"
2. 查看现有分类或点击"添加分类"
3. 选择图标、设置颜色、输入分类名称
4. 保存后即可在创建提示词时使用

### 图片插入
- **工具栏上传**：点击图片图标选择文件
- **拖拽上传**：直接拖拽图片到编辑器
- **剪贴板粘贴**：Ctrl+V粘贴截图或复制的图片

### 主题切换
1. 点击右上角的调色板图标
2. 选择喜欢的主题背景
3. 主题会自动保存到本地

## 🔮 默认分类

系统预设了以下分类，方便快速开始：

- 🔧 **编程**：编程相关的提示词
- 📝 **论文提示词**：学术写作和论文相关
- 🔧 **cursor mcp等**：开发工具和插件相关
- 💼 **工作效率**：提高工作效率的提示词
- ✍️ **创意写作**：创意写作和文案相关
- ⚙️ **技术开发**：技术开发和编程相关
- 📚 **学习教育**：学习和教育相关
- 📈 **营销推广**：市场营销和推广相关
- 🎨 **设计创意**：设计和创意相关
- 📊 **数据分析**：数据分析和统计相关
- 🤝 **客户服务**：客户服务和沟通相关

## 💡 使用技巧

1. **快速搜索**：使用搜索框快速找到需要的提示词
2. **标签分类**：为提示词添加相关标签，便于后续查找
3. **Markdown预览**：编辑时可随时切换到预览模式查看效果
4. **批量管理**：通过分类筛选批量查看同类提示词
5. **主题选择**：根据使用环境选择合适的主题背景

## 🔧 自定义配置

### 修改默认端口
编辑 `backend/main.py` 文件的最后几行：
```python
uvicorn.run("backend.main:app", host="127.0.0.1", port=5022, reload=True)
```

### 添加新的默认分类
编辑 `backend/main.py` 中的 `default_categories` 列表。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证 - 查看LICENSE文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 优秀的Python Web框架
- [Marked.js](https://marked.js.org/) - 强大的Markdown解析器
- [Lucide Icons](https://lucide.dev/) - 美观的图标库

---

如果这个工具对您有帮助，请给个⭐️支持一下！ 