/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary: #6366f1;
    --primary-hover: #5b5bdb;
    --secondary: #64748b;
    --danger: #ef4444;
    --success: #10b981;
    --warning: #f59e0b;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-2: #f1f5f9;
    --border: #e2e8f0;
    --text: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius: 8px;
    --sidebar-width: 320px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 30%, #ddd6fe 70%, #c4b5fd 100%);
    min-height: 100vh;
    color: var(--text);
    line-height: 1.6;
}

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 左侧边栏 - 优化版 */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 2px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
    padding: 0 0.5rem;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 2px solid #f1f5f9;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    margin: 0 -0.5rem 1.5rem -0.5rem;
    border-radius: 0 0 20px 20px;
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.025em;
}

.new-prompt-btn {
    width: 100%;
    padding: 1.125rem 1.5rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

.new-prompt-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.new-prompt-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.new-prompt-btn:hover::before {
    left: 100%;
}

/* 搜索栏 - 优化版 */
.search-container {
    padding: 1.5rem;
    border-bottom: 2px solid #f1f5f9;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 1rem;
    color: #6b7280;
    width: 18px;
    height: 18px;
    transition: color 0.3s ease;
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: 1rem 1.25rem 1rem 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    background: #f8fafc;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.search-box input:focus {
    outline: none;
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.search-box input:focus + i {
    color: #6366f1;
}

.search-box input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* 筛选区域 - 优化版 */
.filter-section {
    padding: 1.25rem 1.5rem;
    border-bottom: 2px solid #f1f5f9;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.filter-section h3 {
    font-size: 0.95rem;
    font-weight: 700;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.manage-categories-btn {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.manage-categories-btn:hover {
    background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.filter-tags-container {
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
}

.filter-tags-container::-webkit-scrollbar {
    width: 4px;
}

.filter-tags-container::-webkit-scrollbar-track {
    background: transparent;
}

.filter-tags-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 2px;
}

.filter-tags {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
    max-height: 450px;
    overflow-y: auto;
    padding-right: 0.25rem;
}

.filter-tags::-webkit-scrollbar {
    width: 4px;
}

.filter-tags::-webkit-scrollbar-track {
    background: var(--surface-2);
    border-radius: 2px;
}

.filter-tags::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 2px;
}

.filter-tags::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

.filter-tag {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    background: #f8fafc;
    color: #6b7280;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.filter-tag::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: transparent;
    transition: all 0.3s ease;
}

.filter-tag:hover {
    background: #f1f5f9;
    color: #374151;
    border-color: #e5e7eb;
    transform: translateX(4px);
}

.filter-tag.active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-color: #6366f1;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.filter-tag.active::before {
    background: rgba(255, 255, 255, 0.3);
    width: 6px;
}

.filter-tag-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-tag-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.filter-tag:not(.active) .filter-tag-count {
    background: var(--text-muted);
    color: white;
}

.add-category-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
}

.add-category-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
    transition: left 0.5s ease;
}

.add-category-btn:hover {
    border-color: #10b981;
    color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.add-category-btn:hover::before {
    left: 100%;
}

/* 标签颜色配置 */
.color-config-section {
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid var(--border);
}

.color-config-section h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.color-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.color-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
}

.color-option input[type="radio"] {
    margin: 0;
}

.custom-color-palette {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.color-picker-row {
    display: flex;
    gap: 0.5rem;
}

.color-picker {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
}

/* 统计区域 - 优化版 */
.stats-section {
    padding: 1.5rem;
    margin-top: auto;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px 16px 0 0;
    border-top: 2px solid #e2e8f0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.875rem 1rem;
    background: white;
    border-radius: 12px;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-color: #e5e7eb;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 600;
}

/* 主内容区 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: white;
}

/* 内容头部 - 优化版 */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    border-bottom: none;
    margin-bottom: 1.5rem;
    border-radius: 0 0 24px 24px;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.2);
    position: relative;
    overflow: hidden;
}

.content-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.content-header .header-left,
.content-header .header-right {
    position: relative;
    z-index: 1;
}

.content-header .header-left h1 {
    color: white;
    font-size: 1.75rem;
    font-weight: 800;
    letter-spacing: -0.025em;
    margin: 0;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 2;
}

.content-header .results-count {
    color: rgba(255, 255, 255, 0.95);
    font-size: 0.95rem;
    font-weight: 600;
    margin-top: 0.25rem;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: inherit;
}

.results-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    z-index: 2;
}

/* 夜间模式切换按钮 - 改进版 */
.dark-mode-btn {
    padding: 0.75rem;
    border: 2px solid rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    color: #fbbf24;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    overflow: hidden;
}

.dark-mode-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.dark-mode-btn:hover {
    background: rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    color: #f59e0b;
}

.dark-mode-btn:hover::before {
    opacity: 0.1;
}

/* 夜间模式下的按钮样式 */
body.dark-mode .dark-mode-btn {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(100, 116, 139, 0.8);
    color: #fbbf24;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

body.dark-mode .dark-mode-btn:hover {
    background: rgba(51, 65, 85, 0.9);
    border-color: rgba(148, 163, 184, 0.9);
    color: #fde047;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}

/* 夜间模式样式 - 重新设计 */
body {
    transition: all 0.5s ease;
}

body.dark-mode {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;
    color: #f1f5f9;
}

/* 为所有元素添加平滑过渡 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 侧边栏夜间模式 */
body.dark-mode .sidebar {
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
    border-right-color: #475569;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

body.dark-mode .sidebar-header {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
    color: white;
}

body.dark-mode .sidebar-header h2 {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

body.dark-mode .new-prompt-btn {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    border: 2px solid #10b981;
}

body.dark-mode .new-prompt-btn:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

body.dark-mode .search-box input {
    background: #334155;
    border-color: #475569;
    color: #f1f5f9;
}

body.dark-mode .search-box input:focus {
    background: #475569;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

body.dark-mode .search-box input::placeholder {
    color: #94a3b8;
}

body.dark-mode .filter-tag {
    background: #334155;
    color: #cbd5e1;
    border-color: #475569;
}

body.dark-mode .filter-tag:hover {
    background: #475569;
    color: #f1f5f9;
    border-color: #64748b;
}

body.dark-mode .filter-tag.active {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
    color: white;
    border-color: #1e40af;
}

body.dark-mode .add-category-btn {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border-color: #64748b;
    color: #cbd5e1;
}

body.dark-mode .add-category-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #10b981;
    color: white;
}

body.dark-mode .stat-item {
    background: #334155;
    border-color: #475569;
}

body.dark-mode .stat-number {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

body.dark-mode .stat-label {
    color: #cbd5e1;
}

/* 主内容区夜间模式 */
body.dark-mode .content-wrapper {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

body.dark-mode .content-header {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #dc2626 100%);
}

body.dark-mode .prompt-card {
    background: #1e293b;
    border: 2px solid #475569;
    color: #f1f5f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body.dark-mode .prompt-card:hover {
    background: #334155;
    border-color: #64748b;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

body.dark-mode .prompt-title {
    color: #f1f5f9;
}

body.dark-mode .prompt-content {
    color: #cbd5e1;
}

body.dark-mode .prompt-category {
    background: #475569;
    color: #cbd5e1;
}

body.dark-mode .prompt-footer {
    color: #94a3b8;
}

/* 模态框夜间模式 */
body.dark-mode .modal {
    background: #1e293b;
    color: #f1f5f9;
    border: 2px solid #475569;
}

body.dark-mode .modal-header {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
    color: white;
}

body.dark-mode .form-group input,
body.dark-mode .form-group textarea,
body.dark-mode .form-group select {
    background: #334155;
    border-color: #475569;
    color: #f1f5f9;
}

body.dark-mode .form-group input:focus,
body.dark-mode .form-group textarea:focus,
body.dark-mode .form-group select:focus {
    background: #475569;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

body.dark-mode .form-group label {
    color: #f1f5f9;
}

body.dark-mode .btn-primary {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
}

body.dark-mode .btn-secondary {
    background: #475569;
    color: #f1f5f9;
    border-color: #64748b;
}

body.dark-mode .theme-menu {
    background: #1e293b;
    border-color: #475569;
}

body.dark-mode .theme-option {
    color: #f1f5f9 !important;
    text-shadow: none;
}

body.dark-mode .theme-option:hover {
    background: #334155;
    color: #f1f5f9 !important;
}

/* 空状态夜间模式 */
body.dark-mode .empty-state {
    color: #cbd5e1;
}

body.dark-mode .empty-state h3 {
    color: #f1f5f9;
}

body.dark-mode .empty-state p {
    color: #94a3b8;
}

body.dark-mode .create-first-btn {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
    color: white;
}

body.dark-mode .create-first-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #8b5cf6 100%);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
}

/* Toast消息夜间模式 */
body.dark-mode .toast {
    background: #1e293b;
    color: #f1f5f9;
    border: 1px solid #475569;
}

body.dark-mode .toast.success {
    background: #064e3b;
    border-color: #059669;
    color: #a7f3d0;
}

body.dark-mode .toast.error {
    background: #7f1d1d;
    border-color: #dc2626;
    color: #fecaca;
}

/* 主题切换器 */
.theme-switcher {
    position: relative;
}

.theme-btn {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.35);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 2;
}

.theme-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

.theme-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    min-width: 140px;
    z-index: 1000;
    margin-top: 0.75rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.theme-option {
    padding: 1rem 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #1f2937 !important;
    font-weight: 700;
    border-bottom: 1px solid #f3f4f6;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.theme-option:last-child {
    border-bottom: none;
}

.theme-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s ease;
}

.theme-option:hover {
    transform: translateX(4px);
    color: #1f2937 !important;
    background: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 3px rgba(255, 255, 255, 1);
}

.theme-option:hover::before {
    left: 100%;
}

.content-header .sort-dropdown select {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.35);
    color: white;
    font-size: 0.9rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 2;
}

.content-header .sort-dropdown select:hover {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

.sort-dropdown select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    color: var(--text);
    font-size: 0.875rem;
}

.view-toggle {
    display: flex;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.content-header .view-btn {
    padding: 0.75rem 1rem;
    border: none;
    background: rgba(255, 255, 255, 0.35);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 700;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
}

.content-header .view-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    color: white;
    transform: translateY(-1px);
}

.content-header .view-btn.active {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 退出登录按钮 */
.logout-btn {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.35);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.8);
    border-color: rgba(239, 68, 68, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: var(--surface);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
}

.view-btn i {
    width: 16px;
    height: 16px;
}

.view-btn:hover {
    background: var(--surface-2);
}

.view-btn.active {
    background: var(--primary);
    color: white;
}

/* 内容包装器 */
.content-wrapper {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: transparent;
}

/* 提示词网格 - 优化版 */
.prompts-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.prompt-card {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(139, 92, 246, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.prompt-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.prompt-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
    background: rgba(255, 255, 255, 1);
}

.prompt-card:hover::before {
    transform: scaleX(1);
}

.prompt-card-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.prompt-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text);
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.prompt-category {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: var(--surface-2);
    color: var(--text-secondary);
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    gap: 0.25rem;
    width: fit-content;
}

.prompt-content {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.prompt-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.prompt-tag {
    padding: 0.125rem 0.5rem;
    background: var(--primary);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.prompt-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.prompt-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.prompt-card:hover .prompt-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all 0.2s;
}

.action-btn:hover {
    background: var(--surface-2);
    color: var(--text);
}

.action-btn i {
    width: 14px;
    height: 14px;
}

/* 分页控件 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding: 1rem 0;
    border-top: 1px solid var(--border);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    background: var(--surface);
    color: var(--text);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--surface-2);
    border-color: var(--primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    background: var(--surface);
    color: var(--text);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    min-width: 40px;
    text-align: center;
}

.page-number:hover {
    background: var(--surface-2);
    border-color: var(--primary);
}

.page-number.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.page-number.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}



/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: 3rem;
}

.empty-icon i {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.create-first-btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.create-first-btn:hover {
    background: var(--primary-hover);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.2s;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.detail-modal {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.detail-actions {
    display: flex;
    gap: 0.5rem;
}

.modal-close {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all 0.2s;
}

.modal-close:hover {
    background: var(--surface-2);
    color: var(--text);
}

.modal-close i {
    width: 20px;
    height: 20px;
}

.modal-body {
    padding: 1.5rem;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: border-color 0.2s;
}

#prompt-title {
    font-size: 1.1rem;
    font-weight: 600;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
}

.form-group textarea {
    min-height: 200px;
    resize: vertical;
    font-family: inherit;
}

.editor-container {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
}

.editor-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border);
    background: var(--surface-2);
    padding: 0.5rem;
}

.editor-toolbar {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.toolbar-btn {
    padding: 0.5rem;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: var(--surface);
    color: var(--text);
}

.toolbar-btn i {
    width: 16px;
    height: 16px;
}

.tab-btn {
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
    margin: -0.5rem 0;
}

.tab-btn.active {
    background: var(--surface);
    color: var(--primary);
    font-weight: 600;
}

.editor-container textarea {
    border: none;
    border-radius: 0;
    min-height: 300px;
    transition: all 0.2s;
}

.editor-container textarea.drag-over {
    background: rgba(99, 102, 241, 0.1);
    border: 2px dashed var(--primary);
}

.content-preview {
    padding: 1rem;
    border: none;
    background: var(--surface);
    min-height: 300px;
    overflow-y: auto;
}

.content-preview h1,
.content-preview h2,
.content-preview h3,
.content-preview h4,
.content-preview h5,
.content-preview h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.content-preview h1 { font-size: 1.75rem; }
.content-preview h2 { font-size: 1.5rem; }
.content-preview h3 { font-size: 1.25rem; }
.content-preview h4 { font-size: 1.125rem; }

.content-preview p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.content-preview ul,
.content-preview ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.content-preview li {
    margin-bottom: 0.25rem;
}

.content-preview blockquote {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--surface-2);
    border-left: 4px solid var(--primary);
    border-radius: 0 var(--radius) var(--radius) 0;
}

.content-preview code {
    background: var(--surface-2);
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-size: 0.875em;
}

.content-preview pre {
    background: var(--surface-2);
    padding: 1rem;
    border-radius: var(--radius);
    overflow-x: auto;
    margin: 1rem 0;
}

.content-preview pre code {
    background: none;
    padding: 0;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border);
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--surface);
    color: var(--text);
}

.btn-secondary:hover {
    background: var(--surface-2);
}

.btn-outline {
    background: var(--surface);
    color: var(--text);
    border-color: var(--border);
}

.btn-outline:hover {
    background: var(--surface-2);
}

.btn-danger {
    background: var(--danger);
    color: white;
    border-color: var(--danger);
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* 详情模态框 */
.detail-meta {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.detail-category {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: var(--surface-2);
    color: var(--text-secondary);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
    gap: 0.5rem;
}

.detail-tags-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.detail-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.detail-action-buttons {
    display: flex;
    gap: 0.5rem;
}

.detail-tag {
    padding: 0.25rem 0.75rem;
    background: var(--primary);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.detail-dates {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.detail-content {
    line-height: 1.7;
}

.detail-content h1,
.detail-content h2,
.detail-content h3,
.detail-content h4,
.detail-content h5,
.detail-content h6 {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text);
}

.detail-content h1 { font-size: 1.75rem; }
.detail-content h2 { font-size: 1.5rem; }
.detail-content h3 { font-size: 1.25rem; }
.detail-content h4 { font-size: 1.125rem; }

.detail-content p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.detail-content ul,
.detail-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.detail-content li {
    margin-bottom: 0.25rem;
}

.detail-content blockquote {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--surface-2);
    border-left: 4px solid var(--primary);
    border-radius: 0 var(--radius) var(--radius) 0;
}

.detail-content code {
    background: var(--surface-2);
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-size: 0.875em;
}

.detail-content pre {
    background: var(--surface-2);
    padding: 1rem;
    border-radius: var(--radius);
    overflow-x: auto;
    margin: 1rem 0;
}

.detail-content pre code {
    background: none;
    padding: 0;
}

.detail-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
}

.detail-content th,
.detail-content td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.detail-content th {
    background: var(--surface-2);
    font-weight: 600;
}

.detail-content a {
    color: var(--primary);
    text-decoration: none;
}

.detail-content a:hover {
    text-decoration: underline;
}

.detail-content strong {
    font-weight: 600;
    color: var(--text);
}

.detail-content em {
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border);
    }

    .content-header {
        padding: 1rem;
    }

    .header-right {
        flex-direction: column;
        gap: 0.5rem;
    }

    .prompts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal {
        width: 95%;
        margin: 1rem;
    }
}

/* Toast 通知 */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    pointer-events: none;
}

.toast {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 0.75rem 1rem;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 250px;
    max-width: 400px;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-success {
    border-left: 4px solid var(--success);
}

.toast-error {
    border-left: 4px solid var(--danger);
}

.toast-warning {
    border-left: 4px solid var(--warning);
}

.toast-info {
    border-left: 4px solid var(--primary);
}

.toast-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.toast-icon.success {
    color: var(--success);
}

.toast-icon.error {
    color: var(--danger);
}

.toast-icon.warning {
    color: var(--warning);
}

.toast-icon.info {
    color: var(--primary);
}

.toast-message {
    flex: 1;
    font-size: 14px;
    color: var(--text);
    font-weight: 500;
}

/* 分类管理样式 - 优化版 */
.categories-management {
    margin-top: 1rem;
}

.add-category-form {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.add-category-form .form-row {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 1rem;
    align-items: center;
}

.add-category-form input[type="text"] {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.add-category-form input[type="text"]:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.add-category-form input[type="color"] {
    width: 48px;
    height: 48px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-category-form input[type="color"]:hover {
    border-color: #6366f1;
    transform: scale(1.05);
}

.categories-list {
    display: grid;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 0.5rem;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    background: white;
    border: 2px solid #f1f5f9;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.category-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 5px;
    background: var(--category-color, #6366f1);
    transition: width 0.3s ease;
}

.category-item:hover {
    background: #fafbff;
    border-color: #6366f1;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

.category-item:hover::before {
    width: 8px;
}

.category-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.category-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: var(--category-color, #6366f1);
}

.category-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 1.1rem;
    letter-spacing: -0.025em;
}

.category-actions {
    display: flex;
    gap: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-item:hover .category-actions {
    opacity: 1;
}

.category-actions .action-btn {
    padding: 0.75rem;
    border: none;
    background: #f3f4f6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

.category-actions .action-btn:hover {
    background: #ef4444;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* 彩色标签和分类样式 */
.prompt-category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.prompt-tag-colored {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.375rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    margin: 0.125rem;
}

/* 默认颜色调色板 */
.color-primary { background-color: #6366f1; }
.color-success { background-color: #10b981; }
.color-warning { background-color: #f59e0b; }
.color-danger { background-color: #ef4444; }
.color-info { background-color: #06b6d4; }
.color-purple { background-color: #8b5cf6; }
.color-pink { background-color: #ec4899; }
.color-indigo { background-color: #6366f1; }
.color-teal { background-color: #14b8a6; }
.color-orange { background-color: #f97316; }

/* 添加分类模态框样式 - 优化版 */
.add-category-modal {
    max-width: 650px;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: none;
}

.add-category-modal .modal-header {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    color: white;
    margin: -2rem -2rem 2rem -2rem;
    padding: 2rem 2.5rem;
    border-radius: 20px 20px 0 0;
    position: relative;
    overflow: hidden;
}

.add-category-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.add-category-modal .modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 800;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    color: white;
}

.add-category-modal .modal-close {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.add-category-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 表单样式美化 - 优化版 */
.add-category-modal .form-group {
    margin-bottom: 2rem;
}

.add-category-modal .form-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    letter-spacing: -0.025em;
}

.add-category-modal .form-group input[type="text"] {
    width: 100%;
    padding: 1.25rem 1.5rem;
    border: 3px solid #e5e7eb;
    border-radius: 16px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.add-category-modal .form-group input[type="text"]:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1);
    background: white;
    transform: translateY(-2px);
}

.add-category-modal .form-group input[type="text"]::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.add-category-modal .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px;
    border: 2px solid #e2e8f0;
}

/* 图标选择器样式 */
.icon-selector {
    position: relative;
}

.selected-icon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.5rem;
    border: 3px solid #e5e7eb;
    border-radius: 16px;
    cursor: pointer;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.3s ease;
    min-width: 140px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.selected-icon:hover {
    border-color: #6366f1;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);
    background: white;
    transform: translateY(-2px);
}

.icon-display {
    font-size: 1.75rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: transform 0.2s ease;
}

.icon-arrow {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.icon-selector.open .icon-arrow {
    transform: rotate(180deg);
    color: #6366f1;
}

.icon-picker {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 3px solid #e5e7eb;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin-top: 0.75rem;
    max-height: 320px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
}

.icon-picker::-webkit-scrollbar {
    width: 6px;
}

.icon-picker::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.icon-picker::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.icon-picker::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.75rem;
    padding: 1.5rem;
}

.icon-option {
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid transparent;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    background: #f8fafc;
    position: relative;
    overflow: hidden;
}

.icon-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.icon-option:hover {
    background: white;
    border-color: #6366f1;
    transform: scale(1.15);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.25);
}

.icon-option:hover::before {
    opacity: 0.1;
}

/* 颜色选择器样式 - 优化版 */
.color-selector {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.color-selector input[type="color"] {
    width: 100%;
    height: 60px;
    border: 3px solid #e5e7eb;
    border-radius: 16px;
    cursor: pointer;
    background: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.color-selector input[type="color"]:hover {
    border-color: #6366f1;
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

.color-presets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.color-preset {
    width: 100%;
    height: 48px;
    border-radius: 12px;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: #374151;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.color-preset.selected {
    border-color: #374151;
    box-shadow: 0 0 0 3px rgba(55, 65, 81, 0.3), 0 8px 25px rgba(0, 0, 0, 0.2);
    transform: scale(1.05);
}

.color-preset.selected::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

/* 表单按钮样式 - 优化版 */
.form-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: flex-end;
    margin-top: 2.5rem;
    padding-top: 2rem;
    border-top: 3px solid #f1f5f9;
}

.form-buttons .btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.125rem 2rem;
    border-radius: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 1rem;
    letter-spacing: -0.025em;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-secondary {
    background: #f8fafc;
    color: #6b7280;
    border: 3px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: #374151;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 分类管理模态框样式 - 优化版 */
.category-modal {
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: none;
}

.category-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    margin: -2rem -2rem 2rem -2rem;
    padding: 2rem 2.5rem;
    border-radius: 20px 20px 0 0;
    position: relative;
    overflow: hidden;
}

.category-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.category-modal .modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 800;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    color: white;
}

.category-modal .modal-close {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.category-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}



/* 响应式设计 - 优化版 */
@media (max-width: 768px) {
    .content-header {
        padding: 1.5rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-right {
        flex-direction: row;
        justify-content: space-between;
        gap: 1rem;
    }

    .add-category-modal,
    .category-modal {
        max-width: 95vw;
        margin: 1rem;
        border-radius: 16px;
    }

    .add-category-modal .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .color-presets {
        grid-template-columns: repeat(4, 1fr);
    }

    .icon-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .category-item {
        padding: 1rem;
    }

    .category-actions {
        opacity: 1;
    }

    .form-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .form-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: 1rem;
    }

    .header-right {
        flex-direction: column;
        gap: 0.75rem;
    }

    .add-category-modal .modal-header,
    .category-modal .modal-header {
        padding: 1.5rem;
        margin: -1.5rem -1.5rem 1.5rem -1.5rem;
    }

    .icon-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 0.5rem;
        padding: 1rem;
    }

    .icon-option {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .color-presets {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 主题样式 */
body.theme-warm {
    background: linear-gradient(135deg, #fef3c7, #fde68a) !important;
}

body.theme-cool {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
}

body.theme-green {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0) !important;
}

body.theme-purple {
    background: linear-gradient(135deg, #ede9fe, #ddd6fe) !important;
}

body.theme-dark {
    background: linear-gradient(135deg, #374151, #1f2937) !important;
}

body.theme-dark .sidebar {
    background: rgba(31, 41, 55, 0.9);
    color: #f9fafb;
}

body.theme-dark .content-header {
    background: rgba(31, 41, 55, 0.9);
}

body.theme-dark .prompt-card {
    background: rgba(31, 41, 55, 0.8);
    color: #f9fafb;
    border-color: #4b5563;
}

body.theme-dark .prompt-card:hover {
    background: rgba(31, 41, 55, 0.9);
}

body.theme-dark .modal {
    background: #1f2937;
    color: #f9fafb;
}

body.theme-dark .form-group input,
body.theme-dark .form-group select,
body.theme-dark .form-group textarea {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
} 