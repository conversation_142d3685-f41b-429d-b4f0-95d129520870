/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary: #6366f1;
    --primary-hover: #5b5bdb;
    --secondary: #64748b;
    --danger: #ef4444;
    --success: #10b981;
    --warning: #f59e0b;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-2: #f1f5f9;
    --border: #e2e8f0;
    --text: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius: 8px;
    --sidebar-width: 320px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    background: #f1f5f9;
    min-height: 100vh;
    color: var(--text);
    line-height: 1.6;
}

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background: white;
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
    padding: 0 0.5rem;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
}

.sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text);
}

.new-prompt-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.new-prompt-btn:hover {
    background: var(--primary-hover);
}

/* 搜索栏 */
.search-container {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 0.75rem;
    color: var(--text-muted);
    width: 16px;
    height: 16px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface-2);
    font-size: 0.875rem;
    transition: border-color 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary);
    background: var(--surface);
}

/* 筛选区域 */
.filter-section {
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid var(--border);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.filter-section h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.manage-categories-btn {
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all 0.2s;
}

.manage-categories-btn:hover {
    background: var(--surface-2);
    color: var(--text);
}

.filter-tags-container {
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
}

.filter-tags-container::-webkit-scrollbar {
    width: 4px;
}

.filter-tags-container::-webkit-scrollbar-track {
    background: transparent;
}

.filter-tags-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 2px;
}

.filter-tags {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
    max-height: 450px;
    overflow-y: auto;
    padding-right: 0.25rem;
}

.filter-tags::-webkit-scrollbar {
    width: 4px;
}

.filter-tags::-webkit-scrollbar-track {
    background: var(--surface-2);
    border-radius: 2px;
}

.filter-tags::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 2px;
}

.filter-tags::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

.filter-tag {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
    background: var(--surface-2);
    color: var(--text-secondary);
    border-left: 3px solid transparent;
}

.filter-tag:hover {
    background: var(--border);
    color: var(--text);
}

.filter-tag.active {
    background: var(--primary);
    color: white;
    border-left-color: var(--primary);
}

.filter-tag-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-tag-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.filter-tag:not(.active) .filter-tag-count {
    background: var(--text-muted);
    color: white;
}

.add-category-btn {
    width: 100%;
    padding: 0.5rem;
    background: none;
    border: 1px dashed var(--border);
    border-radius: var(--radius);
    color: var(--text-muted);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.add-category-btn:hover {
    border-color: var(--primary);
    color: var(--primary);
    background: rgba(99, 102, 241, 0.1);
}

/* 标签颜色配置 */
.color-config-section {
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid var(--border);
}

.color-config-section h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.color-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.color-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
}

.color-option input[type="radio"] {
    margin: 0;
}

.custom-color-palette {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.color-picker-row {
    display: flex;
    gap: 0.5rem;
}

.color-picker {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
}

/* 统计区域 */
.stats-section {
    padding: 0.75rem 1.5rem;
    margin-top: auto;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 主内容区 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: white;
}

/* 内容头部 */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: #6366f1;
    border-bottom: 1px solid var(--border);
    margin-bottom: 1rem;
}

.content-header .header-left h1 {
    color: white;
}

.content-header .results-count {
    color: rgba(255, 255, 255, 0.8);
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: inherit;
}

.results-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 主题切换器 */
.theme-switcher {
    position: relative;
}

.theme-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s;
    border-radius: var(--radius);
}

.theme-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.theme-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    min-width: 120px;
    z-index: 1000;
    margin-top: 0.5rem;
}

.theme-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    border-radius: 4px;
    margin: 0.25rem;
    color: #1f2937;
    font-weight: 500;
}

.theme-option:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-header .sort-dropdown select {
    padding: 0.5rem 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.875rem;
}

.sort-dropdown select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    color: var(--text);
    font-size: 0.875rem;
}

.view-toggle {
    display: flex;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
}

.content-header .view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s;
}

.content-header .view-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.content-header .view-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: var(--surface);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
}

.view-btn i {
    width: 16px;
    height: 16px;
}

.view-btn:hover {
    background: var(--surface-2);
}

.view-btn.active {
    background: var(--primary);
    color: white;
}

/* 内容包装器 */
.content-wrapper {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
}

/* 提示词网格 - 3行4列布局 */
.prompts-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.prompt-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.prompt-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: #10b981;
    background: #f0fdf4;
}

.prompt-card-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.prompt-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text);
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.prompt-category {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: var(--surface-2);
    color: var(--text-secondary);
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    gap: 0.25rem;
    width: fit-content;
}

.prompt-content {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.prompt-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.prompt-tag {
    padding: 0.125rem 0.5rem;
    background: var(--primary);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.prompt-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.prompt-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.prompt-card:hover .prompt-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all 0.2s;
}

.action-btn:hover {
    background: var(--surface-2);
    color: var(--text);
}

.action-btn i {
    width: 14px;
    height: 14px;
}

/* 分页控件 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding: 1rem 0;
    border-top: 1px solid var(--border);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    background: var(--surface);
    color: var(--text);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--surface-2);
    border-color: var(--primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    background: var(--surface);
    color: var(--text);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    min-width: 40px;
    text-align: center;
}

.page-number:hover {
    background: var(--surface-2);
    border-color: var(--primary);
}

.page-number.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.page-number.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 分类管理 */
.categories-management {
    max-height: 600px;
    overflow-y: auto;
}

.add-category-form {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.add-category-form .form-row {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 0.5rem;
    align-items: end;
}

.add-category-form input[type="text"] {
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 0.875rem;
}

.add-category-form input[type="color"] {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--surface-2);
    border-radius: var(--radius);
    transition: all 0.2s;
}

.category-item:hover {
    background: var(--border);
}

.category-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.category-icon {
    font-size: 1.25rem;
}

.category-name {
    font-weight: 500;
}

.category-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid var(--border);
}

.category-actions {
    display: flex;
    gap: 0.25rem;
}

.category-actions .action-btn {
    padding: 0.25rem;
    border-radius: 4px;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: 3rem;
}

.empty-icon i {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.create-first-btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.create-first-btn:hover {
    background: var(--primary-hover);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.2s;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.detail-modal {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.detail-actions {
    display: flex;
    gap: 0.5rem;
}

.modal-close {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all 0.2s;
}

.modal-close:hover {
    background: var(--surface-2);
    color: var(--text);
}

.modal-close i {
    width: 20px;
    height: 20px;
}

.modal-body {
    padding: 1.5rem;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: border-color 0.2s;
}

#prompt-title {
    font-size: 1.1rem;
    font-weight: 600;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
}

.form-group textarea {
    min-height: 200px;
    resize: vertical;
    font-family: inherit;
}

.editor-container {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
}

.editor-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border);
    background: var(--surface-2);
    padding: 0.5rem;
}

.editor-toolbar {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.toolbar-btn {
    padding: 0.5rem;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: var(--surface);
    color: var(--text);
}

.toolbar-btn i {
    width: 16px;
    height: 16px;
}

.tab-btn {
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
    margin: -0.5rem 0;
}

.tab-btn.active {
    background: var(--surface);
    color: var(--primary);
    font-weight: 600;
}

.editor-container textarea {
    border: none;
    border-radius: 0;
    min-height: 300px;
    transition: all 0.2s;
}

.editor-container textarea.drag-over {
    background: rgba(99, 102, 241, 0.1);
    border: 2px dashed var(--primary);
}

.content-preview {
    padding: 1rem;
    border: none;
    background: var(--surface);
    min-height: 300px;
    overflow-y: auto;
}

.content-preview h1,
.content-preview h2,
.content-preview h3,
.content-preview h4,
.content-preview h5,
.content-preview h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.content-preview h1 { font-size: 1.75rem; }
.content-preview h2 { font-size: 1.5rem; }
.content-preview h3 { font-size: 1.25rem; }
.content-preview h4 { font-size: 1.125rem; }

.content-preview p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.content-preview ul,
.content-preview ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.content-preview li {
    margin-bottom: 0.25rem;
}

.content-preview blockquote {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--surface-2);
    border-left: 4px solid var(--primary);
    border-radius: 0 var(--radius) var(--radius) 0;
}

.content-preview code {
    background: var(--surface-2);
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-size: 0.875em;
}

.content-preview pre {
    background: var(--surface-2);
    padding: 1rem;
    border-radius: var(--radius);
    overflow-x: auto;
    margin: 1rem 0;
}

.content-preview pre code {
    background: none;
    padding: 0;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border);
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--surface);
    color: var(--text);
}

.btn-secondary:hover {
    background: var(--surface-2);
}

.btn-outline {
    background: var(--surface);
    color: var(--text);
    border-color: var(--border);
}

.btn-outline:hover {
    background: var(--surface-2);
}

.btn-danger {
    background: var(--danger);
    color: white;
    border-color: var(--danger);
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* 详情模态框 */
.detail-meta {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.detail-category {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: var(--surface-2);
    color: var(--text-secondary);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
    gap: 0.5rem;
}

.detail-tags-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.detail-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.detail-action-buttons {
    display: flex;
    gap: 0.5rem;
}

.detail-tag {
    padding: 0.25rem 0.75rem;
    background: var(--primary);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.detail-dates {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.detail-content {
    line-height: 1.7;
}

.detail-content h1,
.detail-content h2,
.detail-content h3,
.detail-content h4,
.detail-content h5,
.detail-content h6 {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text);
}

.detail-content h1 { font-size: 1.75rem; }
.detail-content h2 { font-size: 1.5rem; }
.detail-content h3 { font-size: 1.25rem; }
.detail-content h4 { font-size: 1.125rem; }

.detail-content p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.detail-content ul,
.detail-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.detail-content li {
    margin-bottom: 0.25rem;
}

.detail-content blockquote {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--surface-2);
    border-left: 4px solid var(--primary);
    border-radius: 0 var(--radius) var(--radius) 0;
}

.detail-content code {
    background: var(--surface-2);
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-size: 0.875em;
}

.detail-content pre {
    background: var(--surface-2);
    padding: 1rem;
    border-radius: var(--radius);
    overflow-x: auto;
    margin: 1rem 0;
}

.detail-content pre code {
    background: none;
    padding: 0;
}

.detail-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
}

.detail-content th,
.detail-content td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.detail-content th {
    background: var(--surface-2);
    font-weight: 600;
}

.detail-content a {
    color: var(--primary);
    text-decoration: none;
}

.detail-content a:hover {
    text-decoration: underline;
}

.detail-content strong {
    font-weight: 600;
    color: var(--text);
}

.detail-content em {
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border);
    }

    .content-header {
        padding: 1rem;
    }

    .header-right {
        flex-direction: column;
        gap: 0.5rem;
    }

    .prompts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal {
        width: 95%;
        margin: 1rem;
    }
}

/* Toast 通知 */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    pointer-events: none;
}

.toast {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 0.75rem 1rem;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 250px;
    max-width: 400px;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-success {
    border-left: 4px solid var(--success);
}

.toast-error {
    border-left: 4px solid var(--danger);
}

.toast-warning {
    border-left: 4px solid var(--warning);
}

.toast-info {
    border-left: 4px solid var(--primary);
}

.toast-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.toast-icon.success {
    color: var(--success);
}

.toast-icon.error {
    color: var(--danger);
}

.toast-icon.warning {
    color: var(--warning);
}

.toast-icon.info {
    color: var(--primary);
}

.toast-message {
    flex: 1;
    font-size: 14px;
    color: var(--text);
    font-weight: 500;
}

/* 分类管理样式 */
.categories-management {
    margin-top: 1rem;
}

.add-category-form {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--surface-2);
    border-radius: var(--radius);
}

.add-category-form .form-row {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 0.5rem;
    align-items: center;
}

.add-category-form input[type="text"] {
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
}

.add-category-form input[type="color"] {
    width: 40px;
    height: 36px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    cursor: pointer;
    padding: 0;
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    transition: all 0.2s;
}

.category-item:hover {
    background: var(--surface-2);
    border-color: var(--primary);
}

.category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.category-icon {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

.category-name {
    font-weight: 500;
    color: var(--text);
}

.category-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid var(--border);
}

.category-actions {
    display: flex;
    gap: 0.25rem;
}

.category-actions .action-btn {
    padding: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s;
}

.category-item:hover .category-actions .action-btn {
    opacity: 1;
}

/* 彩色标签和分类样式 */
.prompt-category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.prompt-tag-colored {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.375rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    margin: 0.125rem;
}

/* 默认颜色调色板 */
.color-primary { background-color: #6366f1; }
.color-success { background-color: #10b981; }
.color-warning { background-color: #f59e0b; }
.color-danger { background-color: #ef4444; }
.color-info { background-color: #06b6d4; }
.color-purple { background-color: #8b5cf6; }
.color-pink { background-color: #ec4899; }
.color-indigo { background-color: #6366f1; }
.color-teal { background-color: #14b8a6; }
.color-orange { background-color: #f97316; }

/* 添加分类模态框样式 */
.add-category-modal {
    max-width: 550px;
    max-height: 90vh;
    overflow-y: auto;
}

.add-category-modal .modal-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    margin: -2rem -2rem 2rem -2rem;
    padding: 1.5rem 2rem;
    border-radius: 16px 16px 0 0;
}

.add-category-modal .modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
}

.add-category-modal .modal-close {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    padding: 0.5rem;
    transition: all 0.2s ease;
}

.add-category-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 表单样式美化 */
.add-category-modal .form-group {
    margin-bottom: 1.5rem;
}

.add-category-modal .form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.add-category-modal .form-group input[type="text"] {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #f9fafb;
}

.add-category-modal .form-group input[type="text"]:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    background: white;
    transform: translateY(-1px);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* 图标选择器样式 */
.icon-selector {
    position: relative;
}

.selected-icon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    background: #f9fafb;
    transition: all 0.2s ease;
}

.selected-icon:hover {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    background: white;
}

.icon-display {
    font-size: 1.5rem;
}

.icon-arrow {
    color: #6b7280;
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.icon-picker {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    margin-top: 0.5rem;
    max-height: 280px;
    overflow-y: auto;
}

.icon-picker::-webkit-scrollbar {
    width: 6px;
}

.icon-picker::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.icon-picker::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.icon-picker::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 0.5rem;
    padding: 1rem;
}

.icon-option {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.2s ease;
    background: #f9fafb;
}

.icon-option:hover {
    background: #10b981;
    color: white;
    transform: scale(1.1);
    border-color: #059669;
}

/* 颜色选择器样式 */
.color-selector {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.color-selector input[type="color"] {
    width: 100%;
    height: 50px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    background: none;
    transition: all 0.2s ease;
}

.color-selector input[type="color"]:hover {
    border-color: #10b981;
    transform: scale(1.02);
}

.color-presets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
}

.color-preset {
    width: 100%;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.color-preset:hover {
    transform: scale(1.05);
    border-color: #374151;
}

.color-preset.selected {
    border-color: #374151;
    box-shadow: 0 0 0 2px rgba(55, 65, 81, 0.2);
}

.color-preset.selected::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 1rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

/* 表单按钮样式 */
.form-buttons {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #f3f4f6;
}

.form-buttons .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    font-size: 0.95rem;
}

.btn-primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: #374151;
    transform: translateY(-1px);
}

/* 分类管理模态框样式 */
.category-modal {
    max-width: 500px;
    max-height: 70vh;
    overflow-y: auto;
}

.category-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -2rem -2rem 1.5rem -2rem;
    padding: 1.5rem 2rem;
    border-radius: 16px 16px 0 0;
}

.category-modal .modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
}

.category-modal .modal-close {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    padding: 0.5rem;
    transition: all 0.2s ease;
}

.category-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
}

.empty-categories {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #9ca3af;
    font-size: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #e5e7eb;
    text-align: center;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.category-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--category-color, #6366f1);
    transition: width 0.2s ease;
}

.category-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.category-item:hover::before {
    width: 6px;
}

.category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.category-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-name {
    font-weight: 500;
    color: #374151;
    font-size: 0.95rem;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.category-item:hover .category-actions {
    opacity: 1;
}

.category-actions .action-btn {
    padding: 0.5rem;
    border: none;
    background: #f3f4f6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-actions .action-btn:hover {
    transform: scale(1.1);
}

.delete-btn {
    background: #fee2e2 !important;
    color: #dc2626 !important;
}

.delete-btn:hover {
    background: #fecaca !important;
    transform: scale(1.1) !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
    .category-modal {
        max-width: 95vw;
        margin: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .color-presets {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .category-item {
        padding: 0.75rem;
    }
    
    .category-actions {
        opacity: 1;
    }
}

/* 主题样式 */
body.theme-warm {
    background: linear-gradient(135deg, #fef3c7, #fde68a) !important;
}

body.theme-cool {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
}

body.theme-green {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0) !important;
}

body.theme-purple {
    background: linear-gradient(135deg, #ede9fe, #ddd6fe) !important;
}

body.theme-dark {
    background: linear-gradient(135deg, #374151, #1f2937) !important;
}

body.theme-dark .sidebar {
    background: rgba(31, 41, 55, 0.9);
    color: #f9fafb;
}

body.theme-dark .content-header {
    background: rgba(31, 41, 55, 0.9);
}

body.theme-dark .prompt-card {
    background: rgba(31, 41, 55, 0.8);
    color: #f9fafb;
    border-color: #4b5563;
}

body.theme-dark .prompt-card:hover {
    background: rgba(31, 41, 55, 0.9);
}

body.theme-dark .modal {
    background: #1f2937;
    color: #f9fafb;
}

body.theme-dark .form-group input,
body.theme-dark .form-group select,
body.theme-dark .form-group textarea {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
} 