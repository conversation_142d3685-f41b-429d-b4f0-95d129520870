from typing import List
from sqlalchemy.orm import Session

import models, schemas

# Helper to convert tags - 支持任意输入

def _tags_to_str(tags):
    if tags is None:
        return None
    if isinstance(tags, str):
        return tags  # 直接存储字符串
    if isinstance(tags, list):
        return ",".join(str(t) for t in tags)  # 列表转字符串
    return str(tags)  # 其他类型转字符串


def _str_to_tags(tags_str):
    if tags_str is None or tags_str == "":
        return []
    # 尝试按逗号分割，如果不包含逗号则返回单个标签
    if "," in tags_str:
        return [tag.strip() for tag in tags_str.split(",") if tag.strip()]
    return [tags_str.strip()] if tags_str.strip() else []


def create_prompt(db: Session, prompt: schemas.PromptCreate) -> models.Prompt:
    db_prompt = models.Prompt(
        title=prompt.title or "",  # 确保不为None
        content=prompt.content or "",
        category=prompt.category,
        tags=_tags_to_str(prompt.tags),
    )
    db.add(db_prompt)
    db.commit()
    db.refresh(db_prompt)
    return db_prompt


def get_prompts(db: Session, skip: int = 0, limit: int = 100) -> List[models.Prompt]:
    return db.query(models.Prompt).offset(skip).limit(limit).all()


def get_prompt(db: Session, prompt_id: int) -> models.Prompt | None:
    return db.query(models.Prompt).filter(models.Prompt.id == prompt_id).first()


def update_prompt(db: Session, db_prompt: models.Prompt, updates: schemas.PromptUpdate) -> models.Prompt:
    db_prompt.title = updates.title or ""
    db_prompt.content = updates.content or ""
    db_prompt.category = updates.category
    db_prompt.tags = _tags_to_str(updates.tags)
    db.commit()
    db.refresh(db_prompt)
    return db_prompt


def delete_prompt(db: Session, db_prompt: models.Prompt):
    db.delete(db_prompt)
    db.commit()

# Category CRUD operations
def create_category(db: Session, category: schemas.CategoryCreate) -> models.Category:
    db_category = models.Category(
        name=category.name,
        icon=category.icon,
        color=category.color,
    )
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category


def get_categories(db: Session) -> List[models.Category]:
    return db.query(models.Category).all()


def get_category(db: Session, category_id: int):
    """获取单个分类"""
    return db.query(models.Category).filter(models.Category.id == category_id).first()


def update_category(db: Session, category_id: int, category: schemas.CategoryCreate):
    """更新分类"""
    db_category = db.query(models.Category).filter(models.Category.id == category_id).first()
    if db_category:
        db_category.name = category.name
        db_category.icon = category.icon
        db_category.color = category.color
        db.commit()
        db.refresh(db_category)
    return db_category


def delete_category(db: Session, db_category: models.Category):
    db.delete(db_category)
    db.commit()


# 统计功能
def get_category_stats(db: Session):
    """获取分类统计信息"""
    # 获取所有分类
    categories = get_categories(db)
    
    # 统计每个分类的提示词数量
    category_counts = {}
    for category in categories:
        count = db.query(models.Prompt).filter(models.Prompt.category == category.name).count()
        category_counts[category.name] = count
    
    # 统计无分类的提示词
    uncategorized_count = db.query(models.Prompt).filter(
        (models.Prompt.category == None) | (models.Prompt.category == "")
    ).count()
    
    # 获取总数
    total_prompts = db.query(models.Prompt).count()
    total_categories = len(categories)
    
    return {
        "total_prompts": total_prompts,
        "total_categories": total_categories,
        "category_counts": category_counts,
        "uncategorized_count": uncategorized_count,
        "categories": [
            {
                "id": cat.id,
                "name": cat.name,
                "icon": cat.icon,
                "color": cat.color,
                "count": category_counts.get(cat.name, 0)
            }
            for cat in categories
        ]
    }

def get_categories_count(db: Session):
    """获取分类总数"""
    return db.query(models.Category).count() 