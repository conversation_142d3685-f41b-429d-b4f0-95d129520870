from sqlalchemy import Column, Integer, Text, DateTime
from sqlalchemy.orm import declarative_base
from datetime import datetime

Base = declarative_base()

class Prompt(Base):
    __tablename__ = "prompts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(Text, nullable=True)  # 任意长度，可为空
    content = Column(Text, nullable=True)  # 可为空
    category = Column(Text, nullable=True)  # 任意长度
    tags = Column(Text, nullable=True)  # 任意长度文本
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Category(Base):
    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(Text, nullable=False)  # 分类名称
    icon = Column(Text, nullable=True)  # 图标emoji
    color = Column(Text, nullable=True)  # 自定义颜色
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) 