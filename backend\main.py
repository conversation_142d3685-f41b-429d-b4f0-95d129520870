import os, sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path
from contextlib import asynccontextmanager

import models, schemas, crud, database

# 创建数据库表
models.Base.metadata.create_all(bind=database.engine)

def get_db():
    db = database.SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_default_data():
    """初始化默认数据"""
    db = database.SessionLocal()
    try:
        # 定义完整的默认分类列表
        default_categories = [
            {"name": "编程", "icon": "💻", "color": "#3b82f6"},
            {"name": "论文提示词", "icon": "📝", "color": "#10b981"},
            {"name": "cursor mcp等", "icon": "🔧", "color": "#8b5cf6"},
            {"name": "工作效率", "icon": "💼", "color": "#f59e0b"},
            {"name": "创意写作", "icon": "✍️", "color": "#ef4444"},
            {"name": "技术开发", "icon": "⚙️", "color": "#ec4899"},
            {"name": "学习教育", "icon": "📚", "color": "#06b6d4"},
            {"name": "营销推广", "icon": "📈", "color": "#84cc16"},
            {"name": "设计创意", "icon": "🎨", "color": "#f97316"},
            {"name": "数据分析", "icon": "📊", "color": "#14b8a6"},
            {"name": "客户服务", "icon": "🤝", "color": "#6366f1"},
        ]
        
        # 检查是否已有分类数据
        if crud.get_categories_count(db) == 0:
            # 数据库为空，创建所有默认分类
            for cat_data in default_categories:
                category = schemas.CategoryCreate(**cat_data)
                crud.create_category(db, category)
            print("✅ 默认分类数据初始化完成")
        else:
            # 数据库不为空，检查是否需要添加新分类
            existing_categories = crud.get_categories(db)
            existing_names = {cat.name for cat in existing_categories}
            
            # 添加缺失的默认分类
            added_count = 0
            for cat_data in default_categories:
                if cat_data["name"] not in existing_names:
                    category = schemas.CategoryCreate(**cat_data)
                    crud.create_category(db, category)
                    added_count += 1
            
            if added_count > 0:
                print(f"✅ 添加了 {added_count} 个新的默认分类")
            else:
                print("✅ 所有默认分类已存在")
    finally:
        db.close()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动事件
    print("🚀 应用启动中...")
    init_default_data()
    print("✅ 应用启动完成")
    yield
    # 关闭事件（如需要）
    print("👋 应用关闭")

app = FastAPI(title="AI Prompt 管理工具 API", lifespan=lifespan)

# 允许本地前端跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_db_session():
    db = database.SessionLocal()
    try:
        yield db
    finally:
        db.close()

def _convert_prompt_for_response(db_prompt: models.Prompt) -> dict:
    """转换数据库Prompt对象为响应格式"""
    return {
        "id": db_prompt.id,
        "title": db_prompt.title,
        "content": db_prompt.content,
        "category": db_prompt.category,
        "tags": crud._str_to_tags(db_prompt.tags),
        "created_at": db_prompt.created_at,
        "updated_at": db_prompt.updated_at,
    }

def _convert_category_for_response(db_category: models.Category) -> dict:
    """转换数据库Category对象为响应格式"""
    return {
        "id": db_category.id,
        "name": db_category.name,
        "icon": db_category.icon,
        "color": db_category.color,
        "created_at": db_category.created_at,
        "updated_at": db_category.updated_at,
    }

# Prompt API endpoints
@app.post("/api/prompts/", response_model=schemas.PromptOut)
async def create_prompt(prompt: schemas.PromptCreate, db: Session = Depends(get_db_session)):
    db_prompt = crud.create_prompt(db, prompt)
    return _convert_prompt_for_response(db_prompt)

@app.get("/api/prompts/", response_model=list[schemas.PromptOut])
async def list_prompts(skip: int = 0, limit: int = 100, db: Session = Depends(get_db_session)):
    db_prompts = crud.get_prompts(db, skip, limit)
    return [_convert_prompt_for_response(p) for p in db_prompts]

@app.get("/api/prompts/{prompt_id}", response_model=schemas.PromptOut)
async def read_prompt(prompt_id: int, db: Session = Depends(get_db_session)):
    db_prompt = crud.get_prompt(db, prompt_id)
    if not db_prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    return _convert_prompt_for_response(db_prompt)

@app.put("/api/prompts/{prompt_id}", response_model=schemas.PromptOut)
async def update_prompt(prompt_id: int, prompt: schemas.PromptUpdate, db: Session = Depends(get_db_session)):
    db_prompt = crud.get_prompt(db, prompt_id)
    if not db_prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    updated_prompt = crud.update_prompt(db, db_prompt, prompt)
    return _convert_prompt_for_response(updated_prompt)

@app.delete("/api/prompts/{prompt_id}")
async def delete_prompt(prompt_id: int, db: Session = Depends(get_db_session)):
    db_prompt = crud.get_prompt(db, prompt_id)
    if not db_prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    crud.delete_prompt(db, db_prompt)
    return {"detail": "Deleted"}

# Category API endpoints
@app.post("/api/categories/", response_model=schemas.CategoryOut)
async def create_category(category: schemas.CategoryCreate, db: Session = Depends(get_db_session)):
    db_category = crud.create_category(db, category)
    return _convert_category_for_response(db_category)

@app.get("/api/categories/", response_model=list[schemas.CategoryOut])
async def list_categories(db: Session = Depends(get_db_session)):
    db_categories = crud.get_categories(db)
    return [_convert_category_for_response(c) for c in db_categories]

@app.get("/api/categories/{category_id}", response_model=schemas.CategoryOut)
async def read_category(category_id: int, db: Session = Depends(get_db_session)):
    db_category = crud.get_category(db, category_id)
    if not db_category:
        raise HTTPException(status_code=404, detail="Category not found")
    return _convert_category_for_response(db_category)

@app.put("/api/categories/{category_id}", response_model=schemas.CategoryOut)
async def update_category(category_id: int, category: schemas.CategoryCreate, db: Session = Depends(get_db_session)):
    db_category = crud.get_category(db, category_id)
    if not db_category:
        raise HTTPException(status_code=404, detail="Category not found")
    updated_category = crud.update_category(db, category_id, category)
    return _convert_category_for_response(updated_category)

@app.delete("/api/categories/{category_id}")
async def delete_category(category_id: int, db: Session = Depends(get_db_session)):
    db_category = crud.get_category(db, category_id)
    if not db_category:
        raise HTTPException(status_code=404, detail="Category not found")
    crud.delete_category(db, db_category)
    return {"detail": "Deleted"}



# 统计API
@app.get("/api/stats/categories")
async def get_category_stats(db: Session = Depends(get_db_session)):
    """获取分类统计信息"""
    stats = crud.get_category_stats(db)
    return stats

# 挂载前端静态文件 - API路由注册后再挂载
frontend_path = Path(__file__).resolve().parent.parent / "frontend"
app.mount("/", StaticFiles(directory=str(frontend_path), html=True), name="frontend")

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("backend.main:app", host="127.0.0.1", port=5022, reload=True)
