// 登录检查
function checkLogin() {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const loginTime = localStorage.getItem('loginTime');

    // 检查是否登录以及登录是否过期（24小时）
    if (!isLoggedIn || !loginTime || (new Date().getTime() - parseInt(loginTime)) > 24 * 60 * 60 * 1000) {
        // 清除过期的登录状态
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('loginTime');
        // 跳转到登录页面
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('loginTime');
        window.location.href = 'login.html';
    }
}

// 全局变量
let prompts = [];
let categories = [];
let currentPrompt = null;
let currentFilter = 'all';
let currentCategory = null;
let currentView = 'grid'; // 添加当前视图状态

// 预定义颜色
const PRESET_COLORS = [
    '#6366f1', '#10b981', '#f59e0b', '#ef4444',
    '#06b6d4', '#8b5cf6', '#ec4899', '#14b8a6',
    '#f97316', '#84cc16'
];

// 预定义图标
const ICON_CATEGORIES = {
    '计算机编程': ['💻', '⌨️', '🖥️', '📱', '⚙️', '🔧', '🛠️', '🐛', '🚀', '⚡', '🔗', '📦', '🗄️', '🌐', '🔒', '🧪', '🎮', '🤖', '📡', '💾'],
    '论文提示词': ['📄', '📑', '📰', '📋', '📊', '📈', '📉', '🔍', '🔬', '📝', '✍️', '📚', '📖', '🎓', '💡', '🧠', '📜', '📌', '🔖', '📎'],
    'Cursor提示词': ['🖱️', '⌨️', '💻', '🖥️', '💡', '⚡', '🎯', '🚀', '🔥', '✨', '🎨', '🛠️', '⚙️', '🔧', '📝', '💾', '🗂️', '📋', '🎪', '🌟'],
    '工作办公': ['💼', '📊', '📈', '📋', '🗂️', '📝', '🖥️', '⌨️', '🖱️', '📞', '📧', '📅', '⏰', '💡', '🎯'],
    '学习教育': ['📚', '📖', '✏️', '📓', '📒', '🎓', '🔬', '🧮', '📐', '🌟', '💭', '🧠', '📜', '🎨', '🔍'],
    '生活娱乐': ['🎮', '🎵', '🎬', '📷', '🎪', '🎨', '🏆', '🎯', '🎲', '🃏', '🎳', '⚽', '🏀', '🎸', '🎤'],
    '技术开发': ['💻', '🖥️', '⌨️', '🖱️', '💾', '💿', '📱', '⚙️', '🔧', '🛠️', '🔩', '⚡', '🌐', '📡', '🔒'],
    '创意设计': ['🎨', '✨', '🌈', '🎭', '🖌️', '🖍️', '🎪', '🦄', '🌟', '💫', '🔮', '🎯', '🎬', '📸', '🖼️'],
    '健康医疗': ['⚕️', '💊', '🏥', '🩺', '💉', '🧬', '🔬', '🧪', '🧘', '💚', '❤️', '🫀', '🧠', '👁️', '🦷'],
    '交通出行': ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️'],
    '美食饮品': ['🍔', '🍕', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙', '🍝', '🍜', '🍲', '🍱', '🍘', '🍙', '🍚'],
    '动物自然': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🦁', '🐯', '🐨', '🐸', '🐵', '🦋', '🌸'],
    '表情符号': ['😊', '😍', '🤔', '😎', '🥳', '😴', '🤗', '😂', '🤯', '🥺', '😇', '🤖', '👻', '💀', '🎃']
};

// Markdown编辑器工具函数
function insertMarkdown(before, after) {
    const textarea = document.getElementById('prompt-content');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    
    const replacement = before + selectedText + after;
    
    textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
    
    // 重新设置光标位置
    const newCursorPos = start + before.length + selectedText.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();
}

function triggerImageUpload() {
    document.getElementById('image-upload').click();
}

function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        showToast('请选择图片文件', 'error');
        return;
    }
    
    // 检查文件大小 (5MB限制)
    if (file.size > 5 * 1024 * 1024) {
        showToast('图片大小不能超过5MB', 'error');
        return;
    }
    
    // 询问用户选择插入方式
    const useBase64 = confirm(
        '图片插入方式选择：\n\n' +
        '确定(OK) = Base64嵌入 (图片完全包含在文档中，但会生成较长字符串)\n' +
        '取消(Cancel) = 占位符模式 (显示友好的图片标记，适合预览)'
    );
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const textarea = document.getElementById('prompt-content');
        const filename = file.name.replace(/\.[^/.]+$/, "");
        
        let markdownImage;
        if (useBase64) {
            // Base64嵌入模式
            markdownImage = `![${filename}](${e.target.result})`;
        } else {
            // 占位符模式 - 生成友好的显示格式
            const fileSize = (file.size / 1024).toFixed(1) + 'KB';
            markdownImage = `![🖼️ ${filename} (${fileSize})](# "图片: ${filename}")`;
        }
        
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);
        
        textarea.value = textBefore + markdownImage + textAfter;
        
        // 设置光标位置到插入内容的末尾
        const newCursorPos = cursorPos + markdownImage.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
        
        const mode = useBase64 ? 'Base64模式' : '占位符模式';
        showToast(`图片已插入 (${mode})`, 'success');
    };
    
    reader.onerror = function() {
        showToast('图片读取失败', 'error');
    };
    
    reader.readAsDataURL(file);
    
    // 清空input值，允许重新选择同一文件
    event.target.value = '';
}

// 夜间模式切换功能 - 改进版
function toggleDarkMode() {
    const body = document.body;
    const icon = document.getElementById('dark-mode-icon');
    const btn = document.querySelector('.dark-mode-btn');

    // 添加切换动画
    btn.style.transform = 'scale(0.9)';

    setTimeout(() => {
        if (body.classList.contains('dark-mode')) {
            body.classList.remove('dark-mode');
            icon.setAttribute('data-lucide', 'moon');
            localStorage.setItem('darkMode', 'false');
            showToast('已切换到日间模式', 'success');
        } else {
            body.classList.add('dark-mode');
            icon.setAttribute('data-lucide', 'sun');
            localStorage.setItem('darkMode', 'true');
            showToast('已切换到夜间模式', 'success');
        }

        // 重新创建图标
        lucide.createIcons();

        // 恢复按钮大小
        btn.style.transform = 'scale(1)';
    }, 150);
}

// 加载保存的夜间模式设置
function loadDarkMode() {
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    const body = document.body;
    const icon = document.getElementById('dark-mode-icon');

    if (isDarkMode) {
        body.classList.add('dark-mode');
        if (icon) {
            icon.setAttribute('data-lucide', 'sun');
        }
    } else {
        body.classList.remove('dark-mode');
        if (icon) {
            icon.setAttribute('data-lucide', 'moon');
        }
    }

    // 重新创建图标
    lucide.createIcons();
}

// 主题切换功能
function toggleThemeMenu() {
    const menu = document.getElementById('theme-menu');
    menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
}

// 点击其他地方关闭主题菜单
document.addEventListener('click', function(e) {
    const themeSwitcher = document.querySelector('.theme-switcher');
    if (themeSwitcher && !themeSwitcher.contains(e.target)) {
        document.getElementById('theme-menu').style.display = 'none';
    }
});

// 应用主题
function applyTheme(theme) {
    const body = document.body;
    
    // 移除所有主题类
    body.classList.remove('theme-warm', 'theme-cool', 'theme-green', 'theme-purple', 'theme-dark');
    
    switch(theme) {
        case 'warm':
            body.classList.add('theme-warm');
            body.style.background = 'linear-gradient(135deg, #fef3c7, #fde68a)';
            break;
        case 'cool':
            body.classList.add('theme-cool');
            body.style.background = 'linear-gradient(135deg, #dbeafe, #bfdbfe)';
            break;
        case 'green':
            body.classList.add('theme-green');
            body.style.background = 'linear-gradient(135deg, #d1fae5, #a7f3d0)';
            break;
        case 'purple':
            body.classList.add('theme-purple');
            body.style.background = 'linear-gradient(135deg, #ede9fe, #ddd6fe)';
            break;
        case 'dark':
            body.classList.add('theme-dark');
            body.style.background = 'linear-gradient(135deg, #374151, #1f2937)';
            break;
        default:
            body.style.background = '#f1f5f9';
    }
    
    // 保存主题选择
    localStorage.setItem('selectedTheme', theme);
    
    // 关闭菜单
    const themeMenu = document.getElementById('theme-menu');
    if (themeMenu) {
        themeMenu.style.display = 'none';
    }
}

// 绑定主题选项点击事件
function bindThemeEvents() {
    document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', function() {
            const theme = this.dataset.theme;
            applyTheme(theme);
        });
    });
}

// 加载保存的主题
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('selectedTheme') || 'default';
    applyTheme(savedTheme);
}

// 初始化剪贴板粘贴功能
function initClipboardPaste() {
    const contentTextarea = document.getElementById('prompt-content');
    if (contentTextarea) {
        contentTextarea.addEventListener('paste', handlePaste);
    }
}

// 处理粘贴事件
async function handlePaste(event) {
    const items = event.clipboardData.items;
    
    for (let item of items) {
        if (item.type.indexOf('image') !== -1) {
            event.preventDefault();
            const file = item.getAsFile();
            
            if (file && file.size <= 5 * 1024 * 1024) { // 5MB限制
                try {
                    const base64 = await fileToBase64(file);
                    const imageMarkdown = `![图片](${base64})`;
                    insertTextAtCursor(event.target, imageMarkdown);
                    showSuccess('图片已从剪贴板插入！');
                } catch (error) {
                    showError('图片处理失败：' + error.message);
                }
            } else {
                showError('图片文件过大，请选择小于 5MB 的图片');
            }
            break;
        }
    }
}

// 在光标位置插入文本
function insertTextAtCursor(textarea, text) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const value = textarea.value;
    
    textarea.value = value.substring(0, start) + text + value.substring(end);
    textarea.selectionStart = textarea.selectionEnd = start + text.length;
    textarea.focus();
}

// 文件转Base64的辅助函数
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 首先检查登录状态
    if (!checkLogin()) {
        return; // 如果未登录，直接返回，不执行后续初始化
    }

    lucide.createIcons();
    bindEvents();
    bindThemeEvents();
    loadSavedTheme();
    loadDarkMode(); // 加载夜间模式设置
    loadData();
    initClipboardPaste();
});

// 绑定事件
function bindEvents() {
    // 新建提示词按钮
    document.getElementById('new-prompt-btn').addEventListener('click', openModal);
    
    // 表单提交
    document.getElementById('prompt-form').addEventListener('submit', handleFormSubmit);
    
    // 搜索
    document.getElementById('search-input').addEventListener('input', filterPrompts);
    
    // 排序
    document.getElementById('sort-select').addEventListener('change', filterPrompts);
    
    // 模态框关闭
    document.getElementById('modal-overlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
    
    // 关闭按钮事件绑定
    const modalCloseBtn = document.getElementById('modal-close-btn');
    const modalCancelBtn = document.getElementById('modal-cancel-btn');
    
    if (modalCloseBtn) {
        modalCloseBtn.addEventListener('click', closeModal);
    }
    if (modalCancelBtn) {
        modalCancelBtn.addEventListener('click', closeModal);
    }
    
    // 空状态创建按钮
    const createFirstBtn = document.getElementById('create-first-btn');
    if (createFirstBtn) {
        createFirstBtn.addEventListener('click', openModal);
    }
    
    // 详情模态框关闭
    const detailModalOverlay = document.getElementById('detail-modal-overlay');
    const detailCloseBtn = document.getElementById('detail-close-btn');
    
    if (detailModalOverlay) {
        detailModalOverlay.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
    }
    
    if (detailCloseBtn) {
        detailCloseBtn.addEventListener('click', closeDetailModal);
    }
    
    // 分类管理
    const manageCategoriesBtn = document.getElementById('manage-categories-btn');
    const addCategoryBtn = document.getElementById('add-category-btn');
    const categoryModalOverlay = document.getElementById('category-modal-overlay');
    const categoryModalCloseBtn = document.getElementById('category-modal-close-btn');
    const addCategoryForm = document.getElementById('add-category-form');
    
    if (manageCategoriesBtn) {
        manageCategoriesBtn.addEventListener('click', openCategoryModal);
    }
    
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', openAddCategoryModal);
    }
    
    if (categoryModalOverlay) {
        categoryModalOverlay.addEventListener('click', function(e) {
            if (e.target === this) {
                closeCategoryModal();
            }
        });
    }
    
    if (categoryModalCloseBtn) {
        categoryModalCloseBtn.addEventListener('click', closeCategoryModal);
    }
    
    // 绑定添加分类表单提交事件
    const addCategoryFormElement = document.getElementById('add-category-form');
    if (addCategoryFormElement) {
        addCategoryFormElement.addEventListener('submit', handleAddCategorySubmit);
    }

// 编辑器标签切换
    const editTab = document.querySelector('[data-tab="edit"]');
    const previewTab = document.querySelector('[data-tab="preview"]');
    const contentTextarea = document.getElementById('prompt-content');
    const previewDiv = document.getElementById('content-preview');
    
    if (editTab && previewTab) {
        editTab.addEventListener('click', function() {
            editTab.classList.add('active');
            previewTab.classList.remove('active');
            contentTextarea.style.display = 'block';
            previewDiv.style.display = 'none';
        });
        
        previewTab.addEventListener('click', function() {
            previewTab.classList.add('active');
            editTab.classList.remove('active');
            contentTextarea.style.display = 'none';
            previewDiv.style.display = 'block';
            
            // 使用marked.js渲染markdown
            if (typeof marked !== 'undefined') {
                previewDiv.innerHTML = marked.parse(contentTextarea.value || '暂无内容');
            } else {
                previewDiv.innerHTML = contentTextarea.value || '暂无内容';
            }
        });
    }

    // 添加拖拽上传功能
    if (contentTextarea) {
        contentTextarea.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.add('drag-over');
        });

        contentTextarea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('drag-over');
        });

        contentTextarea.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    // 模拟文件选择事件
                    const fakeEvent = {
                        target: {
                            files: [file],
                            value: ''
                        }
                    };
                    handleImageUpload(fakeEvent);
                } else {
                    showToast('请拖拽图片文件', 'error');
                }
            }
        });
    }

    // 新增：视图切换按钮事件
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const view = e.currentTarget.dataset.view;
            switchView(view);
        });
    });
    
    // 修复：设置按钮应该打开分类管理
    const settingsBtn = document.querySelector('[data-lucide="settings"]');
    if (settingsBtn) {
        settingsBtn.closest('button').addEventListener('click', openCategoryModal);
    }
}

// 加载所有数据
async function loadData() {
    await Promise.all([
        loadPrompts(),
        loadCategories()
    ]);
    updateStats();
    renderFilterTags();
}

// 加载提示词
async function loadPrompts() {
    try {
        const response = await fetch('/api/prompts/');
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        const data = await response.json();
        
        // 检查响应数据格式
        if (data.prompts) {
            prompts = data.prompts;
        } else if (Array.isArray(data)) {
            prompts = data;
        } else {
            console.error('API返回数据格式不正确:', data);
            prompts = [];
        }
        
        renderPrompts();
    } catch (error) {
        console.error('加载提示词失败:', error);
        showError('获取提示词失败，请刷新重试');
        prompts = [];
        renderPrompts();
    }
}

// 加载分类
async function loadCategories() {
    try {
        const response = await fetch('/api/categories/');
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        categories = await response.json();
        updateCategoryOptions();
        renderCategoriesList();
    } catch (error) {
        console.error('加载分类失败:', error);
        categories = [];
    }
}

// 渲染提示词
function renderPrompts() {
    const grid = document.getElementById('prompts-grid');
    const emptyState = document.getElementById('empty-state');
    
    if (prompts.length === 0) {
        grid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }
    
    grid.style.display = 'grid';
    emptyState.style.display = 'none';
    
    grid.innerHTML = prompts.map(prompt => createPromptCard(prompt)).join('');
    
    // 重新创建图标
    lucide.createIcons();
}

// 创建提示词卡片
function createPromptCard(prompt) {
    const tags = Array.isArray(prompt.tags) ? prompt.tags : [];
    const tagsHtml = tags.map(tag => createColoredTag(tag)).join('');
    
    const categoryBadge = prompt.category ? 
        createCategoryBadge(prompt.category) : 
        '<span class="prompt-category-badge color-primary">无分类</span>';
    
    return `
        <div class="prompt-card" onclick="viewPrompt(${prompt.id})">
            <div class="prompt-title">${prompt.title}</div>
            <div class="prompt-meta">
                ${categoryBadge}
                <span>${new Date(prompt.created_at).toLocaleDateString()}</span>
            </div>
            <div class="prompt-content">${prompt.content.substring(0, 100)}${prompt.content.length > 100 ? '...' : ''}</div>
            <div class="prompt-tags">${tagsHtml}</div>
            <div class="prompt-footer">
                <span>创建于 ${new Date(prompt.created_at).toLocaleDateString()}</span>
                <div class="prompt-actions">
                    <button class="action-btn" onclick="event.stopPropagation(); editPrompt(${prompt.id})" title="编辑">
                        <i data-lucide="edit-2"></i>
                    </button>
                    <button class="action-btn" onclick="event.stopPropagation(); deletePrompt(${prompt.id})" title="删除">
                        <i data-lucide="trash-2"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// 创建彩色标签
function createColoredTag(tag) {
    const colorIndex = hashString(tag) % PRESET_COLORS.length;
    const color = PRESET_COLORS[colorIndex];
    return `<span class="prompt-tag-colored" style="background-color: ${color}">${tag}</span>`;
}

// 创建分类徽章
function createCategoryBadge(categoryName) {
    const category = categories.find(c => c.name === categoryName);
    if (category) {
        const color = category.color || PRESET_COLORS[0];
        const icon = category.icon || '';
        return `<span class="prompt-category-badge" style="background-color: ${color}">${icon} ${categoryName}</span>`;
    }
    const colorIndex = hashString(categoryName) % PRESET_COLORS.length;
    const color = PRESET_COLORS[colorIndex];
    return `<span class="prompt-category-badge" style="background-color: ${color}">${categoryName}</span>`;
}

// 字符串哈希函数
function hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
}

// 渲染分类筛选标签
function renderFilterTags() {
    const filterTags = document.getElementById('filter-tags');
    if (!filterTags) return;
    
    // 统计每个分类的提示词数量
    const categoryCounts = {};
    prompts.forEach(prompt => {
        const category = prompt.category || '无分类';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });
    
    let html = `
        <div class="filter-tag ${currentFilter === 'all' ? 'active' : ''}" data-filter="all">
            <span class="filter-tag-name">全部</span>
            <span class="filter-tag-count">${prompts.length}</span>
        </div>
    `;
    
    // 添加分类标签
    categories.forEach(category => {
        const count = categoryCounts[category.name] || 0;
        const color = category.color || PRESET_COLORS[0];
        const icon = category.icon || '';
        html += `
            <div class="filter-tag ${currentFilter === category.name ? 'active' : ''}" 
                 data-filter="${category.name}" 
                 style="border-left-color: ${color}">
                <span class="filter-tag-name">${icon} ${category.name}</span>
                <span class="filter-tag-count">${count}</span>
            </div>
        `;
    });
    
    // 添加无分类
    const uncategorizedCount = categoryCounts['无分类'] || 0;
    if (uncategorizedCount > 0) {
        html += `
            <div class="filter-tag ${currentFilter === '无分类' ? 'active' : ''}" data-filter="无分类">
                <span class="filter-tag-name">无分类</span>
                <span class="filter-tag-count">${uncategorizedCount}</span>
            </div>
        `;
    }
    
    filterTags.innerHTML = html;
    
    // 绑定点击事件
    filterTags.querySelectorAll('.filter-tag').forEach(tag => {
        tag.addEventListener('click', function() {
            const filter = this.dataset.filter;
            setFilter(filter);
        });
    });
}

// 设置筛选
function setFilter(filter) {
    currentFilter = filter;
    filterPrompts();
    renderFilterTags();
}

// 更新统计
function updateStats() {
    // 修复元素ID匹配问题
    const totalPromptsEl = document.getElementById('total-prompts');
    const resultsCountEl = document.getElementById('results-count');
    const categoriesCountEl = document.getElementById('categories-count');
    
    if (totalPromptsEl) {
        totalPromptsEl.textContent = prompts.length;
    }
    if (resultsCountEl) {
        resultsCountEl.textContent = `共 ${prompts.length} 个提示词`;
    }
    if (categoriesCountEl) {
        categoriesCountEl.textContent = categories.length;
    }
}

// 更新分类选项
function updateCategoryOptions() {
    const select = document.getElementById('prompt-category');
    if (!select) return;
    
    select.innerHTML = '<option value="">选择分类</option>';
    
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.name;
        option.textContent = `${category.icon || ''} ${category.name}`;
        select.appendChild(option);
    });
}

// 渲染分类列表
function renderCategoriesList() {
    const categoriesContainer = document.getElementById('categories-list');
    if (!categoriesContainer) return;
    
    if (!categories || categories.length === 0) {
        categoriesContainer.innerHTML = '<div class="empty-categories">暂无分类</div>';
        return;
    }
    
    const categoriesHTML = categories.map(category => `
        <div class="category-item" style="--category-color: ${category.color || '#6366f1'}">
            <div class="category-info">
                <div class="category-icon" style="background-color: ${category.color || '#6366f1'}">
                    ${category.icon || '🏷️'}
                </div>
                <span class="category-name">${category.name}</span>
            </div>
            <div class="category-actions">
                <button class="action-btn delete-btn" onclick="deleteCategory(${category.id})" title="删除">
                    <i data-lucide="trash-2"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    categoriesContainer.innerHTML = categoriesHTML;
    
    // 重新创建图标
    lucide.createIcons();
}

// 添加分类模态框
function openAddCategoryModal() {
    // 重置表单
    document.getElementById('add-category-form').reset();
    document.getElementById('category-color').value = '#6366f1';
    document.querySelector('.icon-display').textContent = '🏷️';
    document.getElementById('category-icon').value = '🏷️';
    
    document.getElementById('add-category-modal-overlay').classList.add('active');
    lucide.createIcons();
}

function closeAddCategoryModal() {
    document.getElementById('add-category-modal-overlay').classList.remove('active');
}

// 分类管理模态框
function openCategoryModal() {
    document.getElementById('category-modal-overlay').classList.add('active');
    
    // 立即加载分类列表
    renderCategoriesList();
    
    // 创建图标
    lucide.createIcons();
}

function closeCategoryModal() {
    document.getElementById('category-modal-overlay').classList.remove('active');
}

// 处理添加分类表单提交
async function handleAddCategorySubmit(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('category-name').value,
        icon: document.getElementById('category-icon').value,
        color: document.getElementById('category-color').value
    };
    
    try {
        const response = await fetch('/api/categories/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            throw new Error('添加失败');
        }
        
        // 重置表单
        document.getElementById('add-category-form').reset();
        document.getElementById('category-color').value = '#6366f1';
        document.querySelector('.icon-display').textContent = '🏷️';
        document.getElementById('category-icon').value = '🏷️';
        
        // 关闭模态框
        closeAddCategoryModal();
        
        // 重新加载数据
        await loadCategories();
        renderFilterTags();
        showSuccess('分类添加成功');
        
    } catch (error) {
        console.error('添加分类失败:', error);
        showError('添加分类失败，请重试');
    }
}

// 图标选择器 - 优化版
function toggleIconPicker() {
    const picker = document.getElementById('icon-picker');
    const arrow = document.querySelector('.icon-arrow');
    const selector = document.querySelector('.icon-selector');
    const isActive = picker.style.display === 'block';

    if (isActive) {
        picker.style.display = 'none';
        arrow.style.transform = 'rotate(0deg)';
        selector.classList.remove('open');
    } else {
        picker.style.display = 'block';
        arrow.style.transform = 'rotate(180deg)';
        selector.classList.add('open');
    }
}

function selectIcon(icon) {
    // 更新显示的图标
    document.querySelector('.icon-display').textContent = icon;
    document.getElementById('category-icon').value = icon;

    // 关闭选择器
    document.getElementById('icon-picker').style.display = 'none';
    document.querySelector('.icon-arrow').style.transform = 'rotate(0deg)';
    document.querySelector('.icon-selector').classList.remove('open');

    // 添加选择动画效果
    const iconDisplay = document.querySelector('.icon-display');
    iconDisplay.style.transform = 'scale(1.2)';
    setTimeout(() => {
        iconDisplay.style.transform = 'scale(1)';
    }, 200);
}

// 颜色选择器
function selectColor(color) {
    document.getElementById('category-color').value = color;
    
    // 更新选中状态
    document.querySelectorAll('.color-preset').forEach(preset => {
        preset.classList.remove('selected');
    });
    document.querySelector(`[data-color="${color}"]`).classList.add('selected');
}

async function deleteCategory(id) {
    if (!confirm('确定要删除这个分类吗？删除后相关提示词的分类将变为"无分类"')) return;
    
    try {
        const response = await fetch(`/api/categories/${id}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            throw new Error('删除失败');
        }
        
        // 重新加载数据并刷新界面
        await loadCategories();
        renderFilterTags();
        renderCategoriesList();  // 刷新分类列表显示
        showSuccess('分类删除成功');
    } catch (error) {
        console.error('删除分类失败:', error);
        showError('删除分类失败，请重试');
    }
}



// 打开模态框 - 兼容HTML中的函数名
function openModal() {
    currentPrompt = null;
    document.getElementById('modal-title').textContent = '新建提示词';
    document.getElementById('prompt-form').reset();
    document.getElementById('modal-overlay').classList.add('active');
    
    // 重置为编辑模式
    const editTab = document.querySelector('[data-tab="edit"]');
    const previewTab = document.querySelector('[data-tab="preview"]');
    const contentTextarea = document.getElementById('prompt-content');
    const previewDiv = document.getElementById('content-preview');
    
    if (editTab && previewTab) {
        editTab.classList.add('active');
        previewTab.classList.remove('active');
        contentTextarea.style.display = 'block';
        previewDiv.style.display = 'none';
    }
}

// 打开提示词模态框 - 兼容HTML中的函数名
function openPromptModal() {
    openModal();
}

// 关闭模态框 - 兼容HTML中的函数名
function closeModal() {
    document.getElementById('modal-overlay').classList.remove('active');
}

function closePromptModal() {
    closeModal();
}

// 关闭详情模态框
function closeDetailModal() {
    document.getElementById('detail-modal-overlay').classList.remove('active');
}

// 打开详情模态框
function openDetailModal(prompt) {
    document.getElementById('detail-title').textContent = prompt.title;
    document.getElementById('detail-category').textContent = prompt.category || '无分类';
    
    // 显示标签
    const tagsContainer = document.getElementById('detail-tags');
    if (prompt.tags && prompt.tags.length > 0) {
        tagsContainer.innerHTML = prompt.tags.map(tag => createColoredTag(tag)).join('');
    } else {
        tagsContainer.innerHTML = '<span class="prompt-tag-colored color-primary">无标签</span>';
    }
    
    // 显示创建和更新时间
    document.getElementById('detail-created').textContent = new Date(prompt.created_at).toLocaleString();
    document.getElementById('detail-updated').textContent = new Date(prompt.updated_at || prompt.created_at).toLocaleString();
    
    // 显示内容（渲染Markdown）
    const contentDiv = document.getElementById('detail-content');
    if (typeof marked !== 'undefined') {
        contentDiv.innerHTML = marked.parse(prompt.content);
    } else {
        contentDiv.innerHTML = `<pre>${prompt.content}</pre>`;
    }
    
    // 绑定编辑和删除按钮事件
    const editBtn = document.getElementById('detail-edit-btn');
    const deleteBtn = document.getElementById('detail-delete-btn');
    
    if (editBtn) {
        editBtn.onclick = () => {
            closeDetailModal();
            editPrompt(prompt.id);
        };
    }
    
    if (deleteBtn) {
        deleteBtn.onclick = () => {
            closeDetailModal();
            deletePrompt(prompt.id);
        };
    }
    
    // 绑定复制和导出按钮
    const copyBtn = document.getElementById('copy-btn');
    const exportBtn = document.getElementById('export-btn');
    
    if (copyBtn) {
        copyBtn.onclick = () => copyPromptContent(prompt);
    }
    
    if (exportBtn) {
        exportBtn.onclick = () => exportPrompt(prompt);
    }
    
    document.getElementById('detail-modal-overlay').classList.add('active');
}

// 复制提示词内容
function copyPromptContent(prompt) {
    navigator.clipboard.writeText(prompt.content).then(() => {
        showSuccess('内容已复制到剪贴板');
    }).catch(err => {
        showError('复制失败');
    });
}

// 导出提示词
function exportPrompt(prompt) {
    const content = `# ${prompt.title}\n\n${prompt.content}`;
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${prompt.title}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showSuccess('导出成功');
}

// 表单提交
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = {
        title: document.getElementById('prompt-title').value,
        content: document.getElementById('prompt-content').value,
        category: document.getElementById('prompt-category').value || null,
        tags: document.getElementById('prompt-tags').value.split(',').map(t => t.trim()).filter(t => t)
    };
    
    try {
        const url = currentPrompt ? `/api/prompts/${currentPrompt.id}` : '/api/prompts/';
        const method = currentPrompt ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            throw new Error('保存失败');
        }
        
        closeModal();
        loadPrompts();
        renderFilterTags();
        showSuccess(currentPrompt ? '更新成功' : '创建成功');
    } catch (error) {
        console.error('保存失败:', error);
        showError('保存失败，请重试');
    }
}

// 编辑提示词
function editPrompt(id) {
    const prompt = prompts.find(p => p.id === id);
    if (!prompt) return;
    
    currentPrompt = prompt;
    document.getElementById('modal-title').textContent = '编辑提示词';
    document.getElementById('prompt-title').value = prompt.title;
    document.getElementById('prompt-content').value = prompt.content;
    document.getElementById('prompt-category').value = prompt.category || '';
    document.getElementById('prompt-tags').value = Array.isArray(prompt.tags) ? prompt.tags.join(', ') : '';
    document.getElementById('modal-overlay').classList.add('active');
    
    // 重置为编辑模式
    const editTab = document.querySelector('[data-tab="edit"]');
    const previewTab = document.querySelector('[data-tab="preview"]');
    const contentTextarea = document.getElementById('prompt-content');
    const previewDiv = document.getElementById('content-preview');
    
    if (editTab && previewTab) {
        editTab.classList.add('active');
        previewTab.classList.remove('active');
        contentTextarea.style.display = 'block';
        previewDiv.style.display = 'none';
    }
}

// 删除提示词
async function deletePrompt(id) {
    if (!confirm('确定要删除这个提示词吗？')) return;
    
    try {
        const response = await fetch(`/api/prompts/${id}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            throw new Error('删除失败');
        }
        
        loadPrompts();
        renderFilterTags();
        showSuccess('删除成功');
    } catch (error) {
        console.error('删除失败:', error);
        showError('删除失败，请重试');
    }
}

// 查看提示词
function viewPrompt(id) {
    const prompt = prompts.find(p => p.id === id);
    if (!prompt) return;
    
    // 使用详情模态框显示
    openDetailModal(prompt);
}

// 过滤提示词
function filterPrompts() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const sortBy = document.getElementById('sort-select').value;
    
    let filtered = prompts.filter(prompt => {
        // 搜索过滤
        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm) ||
                             prompt.content.toLowerCase().includes(searchTerm) ||
                             (prompt.category && prompt.category.toLowerCase().includes(searchTerm));
        
        // 分类过滤
        let matchesCategory = true;
        if (currentFilter !== 'all') {
            if (currentFilter === '无分类') {
                matchesCategory = !prompt.category;
            } else {
                matchesCategory = prompt.category === currentFilter;
            }
        }
        
        return matchesSearch && matchesCategory;
    });
    
    // 排序
    filtered.sort((a, b) => {
        switch (sortBy) {
            case 'created_at':
                return new Date(b.created_at) - new Date(a.created_at);
            case 'updated_at':
                return new Date(b.updated_at || b.created_at) - new Date(a.updated_at || a.created_at);
            case 'title':
                return a.title.localeCompare(b.title);
            default:
                return 0;
        }
    });
    
    // 临时替换prompts数组进行渲染
    const originalPrompts = prompts;
    prompts = filtered;
    renderPrompts();
    prompts = originalPrompts;
    
    // 更新统计显示
    const resultsCountEl = document.getElementById('results-count');
    if (resultsCountEl) {
        resultsCountEl.textContent = `共 ${filtered.length} 个提示词`;
    }
}

// 显示成功消息
function showSuccess(message) {
    console.log('成功:', message);
    showToast(message, 'success');
}

// 显示错误消息
function showError(message) {
    console.error('错误:', message);
    showToast(message, 'error');
}

// 显示警告消息
function showWarning(message) {
    console.warn('警告:', message);
    showToast(message, 'warning');
}

// 显示信息消息
function showInfo(message) {
    console.info('信息:', message);
    showToast(message, 'info');
}

// 通用toast显示函数
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;

    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    // 图标映射
    const icons = {
        success: 'check-circle',
        error: 'x-circle',
        warning: 'alert-triangle',
        info: 'info'
    };
    
    toast.innerHTML = `
        <i class="toast-icon ${type}" data-lucide="${icons[type]}"></i>
        <span class="toast-message">${message}</span>
    `;
    
    // 添加到容器
    toastContainer.appendChild(toast);
    
    // 创建图标
    lucide.createIcons();
    
    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        hideToast(toast);
    }, duration);
    
    // 点击隐藏
    toast.addEventListener('click', () => {
        hideToast(toast);
    });
}

// 隐藏toast
function hideToast(toast) {
    toast.classList.remove('show');
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

// 视图切换功能
function switchView(view) {
    currentView = view;
    
    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.view === view);
    });
    
    // 更新网格容器的类名
    const grid = document.getElementById('prompts-grid');
    grid.className = view === 'grid' ? 'prompts-grid' : 'prompts-list';
    
    // 重新渲染提示词
    renderPrompts();
}

// 图标选择器功能
function initIconSelector() {
    const iconCategories = document.querySelector('.icon-categories');
    if (!iconCategories) return;
    
    // 生成图标分类
    let html = '';
    for (const [categoryName, icons] of Object.entries(ICON_CATEGORIES)) {
        html += `
            <div class="icon-category">
                <div class="icon-category-title">${categoryName}</div>
                <div class="icon-grid">
                    ${icons.map(icon => `
                        <div class="icon-option" data-icon="${icon}" onclick="selectIcon('${icon}')">
                            ${icon}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    iconCategories.innerHTML = html;
    
    // 初始化颜色选择器
    initColorSelector();
}

function toggleIconPicker() {
    const picker = document.getElementById('icon-picker');
    const arrow = document.querySelector('.icon-arrow');
    
    if (picker.style.display === 'none') {
        picker.style.display = 'block';
        arrow.style.transform = 'rotate(180deg)';
        // 如果还没有初始化图标，则初始化
        if (!picker.querySelector('.icon-option')) {
            initIconSelector();
        }
    } else {
        picker.style.display = 'none';
        arrow.style.transform = 'rotate(0deg)';
    }
}

function selectIcon(icon) {
    // 更新显示的图标
    document.querySelector('.icon-display').textContent = icon;
    document.getElementById('category-icon').value = icon;
    
    // 更新选中状态
    document.querySelectorAll('.icon-option').forEach(opt => {
        opt.classList.remove('selected');
    });
    document.querySelector(`[data-icon="${icon}"]`).classList.add('selected');
    
    // 关闭选择器
    document.getElementById('icon-picker').style.display = 'none';
    document.querySelector('.icon-arrow').style.transform = 'rotate(0deg)';
}

function initColorSelector() {
    // 为颜色预设添加点击事件
    document.querySelectorAll('.color-preset').forEach(preset => {
        preset.addEventListener('click', () => {
            const color = preset.dataset.color;
            document.getElementById('category-color').value = color;
            
            // 更新选中状态
            document.querySelectorAll('.color-preset').forEach(p => p.classList.remove('selected'));
            preset.classList.add('selected');
        });
    });
    
    // 监听颜色输入框变化
    document.getElementById('category-color').addEventListener('input', (e) => {
        const color = e.target.value;
        // 检查是否匹配预设颜色
        document.querySelectorAll('.color-preset').forEach(p => {
            p.classList.toggle('selected', p.dataset.color === color);
        });
    });
}

// 点击外部关闭图标选择器
document.addEventListener('click', (e) => {
    const iconSelector = document.querySelector('.icon-selector');
    const iconPicker = document.getElementById('icon-picker');
    
    if (iconSelector && iconPicker && !iconSelector.contains(e.target)) {
        iconPicker.style.display = 'none';
        document.querySelector('.icon-arrow').style.transform = 'rotate(0deg)';
    }
}); 