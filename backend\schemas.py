from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel

class PromptBase(BaseModel):
    title: Optional[str] = None  # 可选字段
    content: Optional[str] = None  # 可选字段
    category: Optional[str] = None
    tags: Optional[Any] = None  # 任意类型，不限制

class PromptCreate(PromptBase):
    pass

class PromptUpdate(PromptBase):
    pass

class PromptOut(PromptBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

class CategoryBase(BaseModel):
    name: str
    icon: Optional[str] = None
    color: Optional[str] = None

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(CategoryBase):
    name: Optional[str] = None

class CategoryOut(CategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    } 