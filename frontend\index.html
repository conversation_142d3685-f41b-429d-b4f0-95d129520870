<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Prompt 管理工具</title>
    <link rel="stylesheet" href="style.css?v=9.0">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 左侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>🎯 Prompt Box</h2>
                <button class="new-prompt-btn" id="new-prompt-btn">
                    <i data-lucide="plus"></i>
                    新建提示词
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-container">
                <div class="search-box">
                    <i data-lucide="search"></i>
                    <input type="text" id="search-input" placeholder="搜索提示词...">
                </div>
            </div>

            <!-- 分类筛选 -->
            <div class="filter-section">
                <div class="filter-header">
                    <h3>分类</h3>
                    <button class="manage-categories-btn" id="manage-categories-btn" title="管理分类">
                        <i data-lucide="settings"></i>
                    </button>
                </div>
                <div class="filter-tags" id="filter-tags">
                    <div class="filter-tag active" data-filter="all">
                        <span class="filter-tag-name">全部</span>
                        <span class="filter-tag-count" id="total-count">0</span>
                    </div>
                    <!-- 动态生成的分类 -->
                </div>
                <button class="add-category-btn" id="add-category-btn">
                    <i data-lucide="plus"></i>
                    添加分类
                </button>
            </div>

            <!-- 统计信息 -->
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number" id="total-prompts">0</span>
                    <span class="stat-label">提示词</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="categories-count">0</span>
                    <span class="stat-label">分类</span>
                </div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 头部工具栏 -->
            <header class="content-header">
                <div class="header-left">
                    <h1>所有提示词</h1>
                    <span class="results-count" id="results-count">共 0 个提示词</span>
                </div>
                <div class="header-right">
                    <button class="dark-mode-btn" onclick="toggleDarkMode()" title="切换夜间模式">
                        <i data-lucide="moon" id="dark-mode-icon"></i>
                    </button>

                    <div class="theme-switcher">
                        <button class="theme-btn" onclick="toggleThemeMenu()" title="切换背景">
                            <i data-lucide="palette"></i>
                        </button>
                        <div class="theme-menu" id="theme-menu" style="display: none;">
                            <div class="theme-option" data-theme="default" style="background: #f8fafc">默认</div>
                            <div class="theme-option" data-theme="warm" style="background: linear-gradient(135deg, #fef3c7, #fde68a)">暖色</div>
                            <div class="theme-option" data-theme="cool" style="background: linear-gradient(135deg, #dbeafe, #bfdbfe)">冷色</div>
                            <div class="theme-option" data-theme="green" style="background: linear-gradient(135deg, #d1fae5, #a7f3d0)">绿色</div>
                            <div class="theme-option" data-theme="purple" style="background: linear-gradient(135deg, #ede9fe, #ddd6fe)">紫色</div>
                            <div class="theme-option" data-theme="dark" style="background: linear-gradient(135deg, #374151, #1f2937)">深色</div>
                        </div>
                    </div>
                    <div class="sort-dropdown">
                        <select id="sort-select">
                            <option value="created_at">最新创建</option>
                            <option value="updated_at">最近更新</option>
                            <option value="title">标题 A-Z</option>
                        </select>
                    </div>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <i data-lucide="grid-3x3"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i data-lucide="list"></i>
                        </button>
                    </div>

                    <button class="logout-btn" onclick="logout()">
                        <i data-lucide="log-out"></i> 退出
                    </button>
                </div>
            </header>

            <!-- 提示词网格 -->
            <div class="prompts-grid" id="prompts-grid">
                <!-- 动态生成的提示词卡片 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i data-lucide="file-text"></i>
                </div>
                <h3>还没有提示词</h3>
                <p>创建您的第一个提示词开始使用</p>
                <button class="create-first-btn" id="create-first-btn">
                    <i data-lucide="plus"></i>
                    创建提示词
                </button>
            </div>
        </main>
    </div>

    <!-- 提示词编辑模态框 -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h2 id="modal-title">新建提示词</h2>
                <button class="modal-close" id="modal-close-btn">
                    <i data-lucide="x"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="prompt-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="prompt-title">标题 *</label>
                            <input type="text" id="prompt-title" required placeholder="为您的提示词起个名字">
                        </div>
                        <div class="form-group">
                            <label for="prompt-category">分类</label>
                            <select id="prompt-category">
                                <option value="">选择分类</option>
                                <!-- 动态生成的分类选项 -->
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="prompt-tags">标签</label>
                        <input type="text" id="prompt-tags" placeholder="用逗号分隔多个标签，如：写作, 创意, 文案">
                    </div>

                    <div class="form-group">
                        <label for="prompt-content">内容 (支持 Markdown) *</label>
                        <div class="editor-container">
                            <div class="editor-tabs">
                                <button type="button" class="tab-btn active" data-tab="edit">编辑</button>
                                <button type="button" class="tab-btn" data-tab="preview">预览</button>
                                <div class="editor-toolbar">
                                    <button type="button" class="toolbar-btn" onclick="insertMarkdown('**', '**')" title="粗体">
                                        <i data-lucide="bold"></i>
                                    </button>
                                    <button type="button" class="toolbar-btn" onclick="insertMarkdown('*', '*')" title="斜体">
                                        <i data-lucide="italic"></i>
                                    </button>
                                    <button type="button" class="toolbar-btn" onclick="insertMarkdown('`', '`')" title="代码">
                                        <i data-lucide="code"></i>
                                    </button>
                                    <button type="button" class="toolbar-btn" onclick="insertMarkdown('### ', '')" title="标题">
                                        <i data-lucide="heading"></i>
                                    </button>
                                    <button type="button" class="toolbar-btn" onclick="insertMarkdown('- ', '')" title="列表">
                                        <i data-lucide="list"></i>
                                    </button>
                                    <button type="button" class="toolbar-btn" onclick="triggerImageUpload()" title="插入图片">
                                        <i data-lucide="image"></i>
                                    </button>
                                    <input type="file" id="image-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                                </div>
                            </div>
                            <textarea id="prompt-content" required placeholder="在此输入您的提示词内容，支持 Markdown 格式..."></textarea>
                            <div id="content-preview" class="content-preview" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="modal-cancel-btn">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i data-lucide="save"></i>
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 提示词详情模态框 -->
    <div class="modal-overlay" id="detail-modal-overlay">
        <div class="modal detail-modal">
            <div class="modal-header">
                <h2 id="detail-title"></h2>
                <div class="detail-actions">
                    <button class="btn btn-outline" id="detail-edit-btn">
                        <i data-lucide="edit-2"></i>
                        编辑
                    </button>
                    <button class="btn btn-danger" id="detail-delete-btn">
                        <i data-lucide="trash-2"></i>
                        删除
                    </button>
                    <button class="modal-close" id="detail-close-btn">
                        <i data-lucide="x"></i>
                    </button>
                </div>
            </div>
            
            <div class="modal-body">
                <div class="detail-meta">
                    <span class="detail-category" id="detail-category"></span>
                    <div class="detail-tags-row">
                        <div class="detail-tags" id="detail-tags"></div>
                        <div class="detail-action-buttons">
                            <button class="btn btn-outline btn-sm" id="copy-btn" title="复制内容">
                                <i data-lucide="copy"></i>
                                复制
                            </button>
                            <button class="btn btn-outline btn-sm" id="export-btn" title="导出为Markdown">
                                <i data-lucide="download"></i>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="detail-dates">
                        <span>创建：<span id="detail-created"></span></span>
                        <span>更新：<span id="detail-updated"></span></span>
                    </div>
                </div>
                <div class="detail-content" id="detail-content"></div>
            </div>
        </div>
    </div>

    <!-- 添加分类模态框 -->
    <div id="add-category-modal-overlay" class="modal-overlay">
        <div id="add-category-modal" class="modal add-category-modal">
            <div class="modal-header">
                <h3>
                    <i data-lucide="plus-circle"></i>
                    添加新分类
                </h3>
                <button class="modal-close" onclick="closeAddCategoryModal()">
                    <i data-lucide="x"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="add-category-form">
                    <div class="form-group">
                        <label for="category-name">
                            <i data-lucide="tag"></i>
                            分类名称
                        </label>
                        <input type="text" id="category-name" name="name" placeholder="输入分类名称..." required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i data-lucide="smile"></i>
                                选择图标
                            </label>
                            <div class="icon-selector">
                                <div class="selected-icon" id="selected-icon" onclick="toggleIconPicker()">
                                    <span class="icon-display">🏷️</span>
                                    <span class="icon-arrow">▼</span>
                                </div>
                                <div class="icon-picker" id="icon-picker" style="display: none;">
                                    <div class="icon-grid">
                                        <!-- 常用标签 -->
                                        <div class="icon-option" onclick="selectIcon('🏷️')">🏷️</div>
                                        <div class="icon-option" onclick="selectIcon('📁')">📁</div>
                                        <div class="icon-option" onclick="selectIcon('💼')">💼</div>
                                        <div class="icon-option" onclick="selectIcon('📋')">📋</div>
                                        
                                        <!-- 技术相关 -->
                                        <div class="icon-option" onclick="selectIcon('💻')">💻</div>
                                        <div class="icon-option" onclick="selectIcon('⚙️')">⚙️</div>
                                        <div class="icon-option" onclick="selectIcon('🔧')">🔧</div>
                                        <div class="icon-option" onclick="selectIcon('🖥️')">🖥️</div>
                                        <div class="icon-option" onclick="selectIcon('📱')">📱</div>
                                        <div class="icon-option" onclick="selectIcon('🌐')">🌐</div>
                                        
                                        <!-- 数据分析 -->
                                        <div class="icon-option" onclick="selectIcon('📊')">📊</div>
                                        <div class="icon-option" onclick="selectIcon('📈')">📈</div>
                                        <div class="icon-option" onclick="selectIcon('📉')">📉</div>
                                        <div class="icon-option" onclick="selectIcon('💹')">💹</div>
                                        
                                        <!-- 创意设计 -->
                                        <div class="icon-option" onclick="selectIcon('🎨')">🎨</div>
                                        <div class="icon-option" onclick="selectIcon('🖌️')">🖌️</div>
                                        <div class="icon-option" onclick="selectIcon('🎭')">🎭</div>
                                        <div class="icon-option" onclick="selectIcon('🎪')">🎪</div>
                                        
                                        <!-- 学习教育 -->
                                        <div class="icon-option" onclick="selectIcon('📚')">📚</div>
                                        <div class="icon-option" onclick="selectIcon('📖')">📖</div>
                                        <div class="icon-option" onclick="selectIcon('🎓')">🎓</div>
                                        <div class="icon-option" onclick="selectIcon('📝')">📝</div>
                                        <div class="icon-option" onclick="selectIcon('✏️')">✏️</div>
                                        <div class="icon-option" onclick="selectIcon('📄')">📄</div>
                                        
                                        <!-- 商业金融 -->
                                        <div class="icon-option" onclick="selectIcon('💰')">💰</div>
                                        <div class="icon-option" onclick="selectIcon('💸')">💸</div>
                                        <div class="icon-option" onclick="selectIcon('💳')">💳</div>
                                        <div class="icon-option" onclick="selectIcon('🏦')">🏦</div>
                                        
                                        <!-- 目标成就 -->
                                        <div class="icon-option" onclick="selectIcon('🎯')">🎯</div>
                                        <div class="icon-option" onclick="selectIcon('⭐')">⭐</div>
                                        <div class="icon-option" onclick="selectIcon('🏆')">🏆</div>
                                        <div class="icon-option" onclick="selectIcon('🥇')">🥇</div>
                                        <div class="icon-option" onclick="selectIcon('🎖️')">🎖️</div>
                                        
                                        <!-- 创新科技 -->
                                        <div class="icon-option" onclick="selectIcon('🚀')">🚀</div>
                                        <div class="icon-option" onclick="selectIcon('💡')">💡</div>
                                        <div class="icon-option" onclick="selectIcon('🔥')">🔥</div>
                                        <div class="icon-option" onclick="selectIcon('⚡')">⚡</div>
                                        <div class="icon-option" onclick="selectIcon('🌟')">🌟</div>
                                        
                                        <!-- 沟通交流 -->
                                        <div class="icon-option" onclick="selectIcon('💬')">💬</div>
                                        <div class="icon-option" onclick="selectIcon('📞')">📞</div>
                                        <div class="icon-option" onclick="selectIcon('📧')">📧</div>
                                        <div class="icon-option" onclick="selectIcon('📢')">📢</div>
                                        
                                        <!-- 工具用品 -->
                                        <div class="icon-option" onclick="selectIcon('🔍')">🔍</div>
                                        <div class="icon-option" onclick="selectIcon('📐')">📐</div>
                                        <div class="icon-option" onclick="selectIcon('📏')">📏</div>
                                        <div class="icon-option" onclick="selectIcon('🔖')">🔖</div>
                                        
                                        <!-- 娱乐休闲 -->
                                        <div class="icon-option" onclick="selectIcon('🎮')">🎮</div>
                                        <div class="icon-option" onclick="selectIcon('🎵')">🎵</div>
                                        <div class="icon-option" onclick="selectIcon('🎬')">🎬</div>
                                        <div class="icon-option" onclick="selectIcon('📷')">📷</div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="category-icon" name="icon" value="🏷️">
                        </div>
                        
                        <div class="form-group">
                            <label for="category-color">
                                <i data-lucide="palette"></i>
                                主题色
                            </label>
                            <div class="color-selector">
                                <input type="color" id="category-color" name="color" value="#6366f1">
                                <div class="color-presets">
                                    <div class="color-preset" style="background: #3b82f6" data-color="#3b82f6" onclick="selectColor('#3b82f6')" title="蓝色"></div>
                                    <div class="color-preset" style="background: #10b981" data-color="#10b981" onclick="selectColor('#10b981')" title="绿色"></div>
                                    <div class="color-preset" style="background: #8b5cf6" data-color="#8b5cf6" onclick="selectColor('#8b5cf6')" title="紫色"></div>
                                    <div class="color-preset" style="background: #f59e0b" data-color="#f59e0b" onclick="selectColor('#f59e0b')" title="橙色"></div>
                                    <div class="color-preset" style="background: #ef4444" data-color="#ef4444" onclick="selectColor('#ef4444')" title="红色"></div>
                                    <div class="color-preset" style="background: #ec4899" data-color="#ec4899" onclick="selectColor('#ec4899')" title="粉色"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-buttons">
                        <button type="button" class="btn btn-secondary" onclick="closeAddCategoryModal()">
                            <i data-lucide="x"></i>
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i data-lucide="plus"></i>
                            添加分类
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 分类管理模态框 -->
    <div id="category-modal-overlay" class="modal-overlay">
        <div id="category-modal" class="modal category-modal">
            <div class="modal-header">
                <h3>
                    <i data-lucide="settings"></i>
                    分类管理
                </h3>
                <button class="modal-close" onclick="closeCategoryModal()">
                    <i data-lucide="x"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div id="categories-list" class="categories-list">
                    <!-- 分类列表将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div class="toast-container" id="toast-container"></div>

    <script src="script.js"></script>
</body>
</html> 